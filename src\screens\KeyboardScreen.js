import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import BluetoothService from '../services/BluetoothService';

const KeyboardScreen = () => {
  const [inputText, setInputText] = useState('');

  const specialKeys = [
    {label: 'Enter', key: 'enter'},
    {label: 'Backspace', key: 'backspace'},
    {label: 'Delete', key: 'delete'},
    {label: 'Tab', key: 'tab'},
    {label: 'Esc', key: 'escape'},
    {label: 'Space', key: 'space'},
  ];

  const functionKeys = [
    'f1', 'f2', 'f3', 'f4', 'f5', 'f6',
    'f7', 'f8', 'f9', 'f10', 'f11', 'f12'
  ];

  const arrowKeys = [
    {label: '↑', key: 'upArrow'},
    {label: '←', key: 'leftArrow'},
    {label: '↓', key: 'downArrow'},
    {label: '→', key: 'rightArrow'},
  ];

  const modifierKeys = [
    {label: 'Ctrl', key: 'ctrl'},
    {label: 'Alt', key: 'alt'},
    {label: 'Cmd', key: 'cmd'},
    {label: 'Shift', key: 'shift'},
  ];

  const sendText = () => {
    if (inputText.trim()) {
      BluetoothService.sendKeyboardData(inputText);
      setInputText('');
      Alert.alert('发送成功', '文本已发送到PC');
    }
  };

  const sendSpecialKey = (key) => {
    BluetoothService.sendSpecialKey(key);
  };

  const renderKeyButton = (item, style = {}) => (
    <TouchableOpacity
      key={item.key || item}
      style={[styles.keyButton, style]}
      onPress={() => sendSpecialKey(item.key || item)}>
      <Text style={styles.keyButtonText}>
        {item.label || item.toUpperCase()}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>文本输入</Text>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入要发送的文本..."
          multiline
          numberOfLines={4}
        />
        <TouchableOpacity style={styles.sendButton} onPress={sendText}>
          <Text style={styles.sendButtonText}>发送文本</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>特殊按键</Text>
        <View style={styles.keyGrid}>
          {specialKeys.map(key => renderKeyButton(key, styles.specialKey))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>功能键</Text>
        <View style={styles.keyGrid}>
          {functionKeys.map(key => renderKeyButton({key, label: key.toUpperCase()}, styles.functionKey))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>方向键</Text>
        <View style={styles.arrowKeyContainer}>
          <View style={styles.arrowKeyRow}>
            {renderKeyButton(arrowKeys[0], styles.arrowKey)}
          </View>
          <View style={styles.arrowKeyRow}>
            {renderKeyButton(arrowKeys[1], styles.arrowKey)}
            {renderKeyButton(arrowKeys[2], styles.arrowKey)}
            {renderKeyButton(arrowKeys[3], styles.arrowKey)}
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>修饰键</Text>
        <View style={styles.keyGrid}>
          {modifierKeys.map(key => renderKeyButton(key, styles.modifierKey))}
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          输入文本后点击发送，或直接使用特殊按键
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 20,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  textInput: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#DDD',
    marginBottom: 15,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  keyGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  keyButton: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginBottom: 10,
    minWidth: 80,
    alignItems: 'center',
  },
  keyButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  specialKey: {
    backgroundColor: '#4CAF50',
    flex: 1,
    marginHorizontal: 2,
  },
  functionKey: {
    backgroundColor: '#FF9800',
    width: '15%',
    marginHorizontal: 2,
  },
  arrowKeyContainer: {
    alignItems: 'center',
  },
  arrowKeyRow: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  arrowKey: {
    backgroundColor: '#9C27B0',
    width: 60,
    height: 60,
    borderRadius: 30,
    marginHorizontal: 5,
    marginVertical: 2,
    justifyContent: 'center',
  },
  modifierKey: {
    backgroundColor: '#607D8B',
    flex: 1,
    marginHorizontal: 2,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default KeyboardScreen;
