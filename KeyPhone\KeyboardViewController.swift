import UIKit

class KeyboardViewController: UIViewController {
    
    private let bluetoothManager = BluetoothManager.shared
    private var textView: UITextView!
    private var specialKeysStackView: UIStackView!
    private var functionKeysStackView: UIStackView!
    private var arrowKeysView: UIView!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupKeyboard()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "键盘控制"
        
        // 文本输入区域
        textView = UITextView()
        textView.backgroundColor = .systemGray6
        textView.layer.cornerRadius = 8
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.delegate = self
        textView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(textView)
        
        // 特殊按键区域
        setupSpecialKeys()
        
        // 功能键区域
        setupFunctionKeys()
        
        // 方向键区域
        setupArrowKeys()
        
        // 设置约束
        NSLayoutConstraint.activate([
            // 文本输入区域
            textView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            textView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            textView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            textView.heightAnchor.constraint(equalToConstant: 120),
            
            // 特殊按键
            specialKeysStackView.topAnchor.constraint(equalTo: textView.bottomAnchor, constant: 20),
            specialKeysStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            specialKeysStackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            specialKeysStackView.heightAnchor.constraint(equalToConstant: 50),
            
            // 功能键
            functionKeysStackView.topAnchor.constraint(equalTo: specialKeysStackView.bottomAnchor, constant: 15),
            functionKeysStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            functionKeysStackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            functionKeysStackView.heightAnchor.constraint(equalToConstant: 40),
            
            // 方向键
            arrowKeysView.topAnchor.constraint(equalTo: functionKeysStackView.bottomAnchor, constant: 20),
            arrowKeysView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            arrowKeysView.widthAnchor.constraint(equalToConstant: 150),
            arrowKeysView.heightAnchor.constraint(equalToConstant: 120)
        ])
    }
    
    private func setupSpecialKeys() {
        specialKeysStackView = UIStackView()
        specialKeysStackView.axis = .horizontal
        specialKeysStackView.distribution = .fillEqually
        specialKeysStackView.spacing = 10
        specialKeysStackView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(specialKeysStackView)
        
        let specialKeys: [(String, SpecialKey)] = [
            ("Tab", .tab),
            ("Enter", .enter),
            ("Backspace", .backspace),
            ("Delete", .delete),
            ("Esc", .escape)
        ]
        
        for (title, key) in specialKeys {
            let button = createSpecialKeyButton(title: title, key: key)
            specialKeysStackView.addArrangedSubview(button)
        }
    }
    
    private func setupFunctionKeys() {
        functionKeysStackView = UIStackView()
        functionKeysStackView.axis = .horizontal
        functionKeysStackView.distribution = .fillEqually
        functionKeysStackView.spacing = 5
        functionKeysStackView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(functionKeysStackView)
        
        let functionKeys: [SpecialKey] = [.f1, .f2, .f3, .f4, .f5, .f6, .f7, .f8, .f9, .f10, .f11, .f12]
        
        for key in functionKeys {
            let button = createSpecialKeyButton(title: key.rawValue.uppercased(), key: key)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            functionKeysStackView.addArrangedSubview(button)
        }
    }
    
    private func setupArrowKeys() {
        arrowKeysView = UIView()
        arrowKeysView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(arrowKeysView)
        
        // 上箭头
        let upButton = createArrowKeyButton(title: "↑", key: .upArrow)
        arrowKeysView.addSubview(upButton)
        
        // 左箭头
        let leftButton = createArrowKeyButton(title: "←", key: .leftArrow)
        arrowKeysView.addSubview(leftButton)
        
        // 下箭头
        let downButton = createArrowKeyButton(title: "↓", key: .downArrow)
        arrowKeysView.addSubview(downButton)
        
        // 右箭头
        let rightButton = createArrowKeyButton(title: "→", key: .rightArrow)
        arrowKeysView.addSubview(rightButton)
        
        // 设置方向键约束
        NSLayoutConstraint.activate([
            // 上箭头
            upButton.topAnchor.constraint(equalTo: arrowKeysView.topAnchor),
            upButton.centerXAnchor.constraint(equalTo: arrowKeysView.centerXAnchor),
            upButton.widthAnchor.constraint(equalToConstant: 50),
            upButton.heightAnchor.constraint(equalToConstant: 40),
            
            // 左箭头
            leftButton.topAnchor.constraint(equalTo: upButton.bottomAnchor, constant: 5),
            leftButton.leadingAnchor.constraint(equalTo: arrowKeysView.leadingAnchor),
            leftButton.widthAnchor.constraint(equalToConstant: 50),
            leftButton.heightAnchor.constraint(equalToConstant: 40),
            
            // 下箭头
            downButton.topAnchor.constraint(equalTo: upButton.bottomAnchor, constant: 5),
            downButton.centerXAnchor.constraint(equalTo: arrowKeysView.centerXAnchor),
            downButton.widthAnchor.constraint(equalToConstant: 50),
            downButton.heightAnchor.constraint(equalToConstant: 40),
            
            // 右箭头
            rightButton.topAnchor.constraint(equalTo: upButton.bottomAnchor, constant: 5),
            rightButton.trailingAnchor.constraint(equalTo: arrowKeysView.trailingAnchor),
            rightButton.widthAnchor.constraint(equalToConstant: 50),
            rightButton.heightAnchor.constraint(equalToConstant: 40)
        ])
        
        // 添加修饰键
        setupModifierKeys()
    }
    
    private func setupModifierKeys() {
        let modifierStackView = UIStackView()
        modifierStackView.axis = .horizontal
        modifierStackView.distribution = .fillEqually
        modifierStackView.spacing = 10
        modifierStackView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(modifierStackView)
        
        let modifierKeys: [(String, SpecialKey)] = [
            ("Ctrl", .ctrl),
            ("Alt", .alt),
            ("Cmd", .cmd),
            ("Shift", .shift)
        ]
        
        for (title, key) in modifierKeys {
            let button = createSpecialKeyButton(title: title, key: key)
            modifierStackView.addArrangedSubview(button)
        }
        
        NSLayoutConstraint.activate([
            modifierStackView.topAnchor.constraint(equalTo: arrowKeysView.bottomAnchor, constant: 20),
            modifierStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            modifierStackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            modifierStackView.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    private func createSpecialKeyButton(title: String, key: SpecialKey) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.translatesAutoresizingMaskIntoConstraints = false
        
        button.addAction(UIAction { _ in
            self.bluetoothManager.sendSpecialKey(key)
            self.animateButtonPress(button)
        }, for: .touchUpInside)
        
        return button
    }
    
    private func createArrowKeyButton(title: String, key: SpecialKey) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        button.backgroundColor = .systemGreen
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.translatesAutoresizingMaskIntoConstraints = false
        
        button.addAction(UIAction { _ in
            self.bluetoothManager.sendSpecialKey(key)
            self.animateButtonPress(button)
        }, for: .touchUpInside)
        
        return button
    }
    
    private func animateButtonPress(_ button: UIButton) {
        UIView.animate(withDuration: 0.1, animations: {
            button.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                button.transform = .identity
            }
        }
    }
    
    private func setupKeyboard() {
        // 添加工具栏到键盘
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        
        let sendButton = UIBarButtonItem(title: "发送", style: .done, target: self, action: #selector(sendText))
        let clearButton = UIBarButtonItem(title: "清空", style: .plain, target: self, action: #selector(clearText))
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        
        toolbar.items = [clearButton, flexSpace, sendButton]
        textView.inputAccessoryView = toolbar
    }
    
    @objc private func sendText() {
        guard let text = textView.text, !text.isEmpty else { return }
        bluetoothManager.sendKeyboardData(text: text)
        textView.text = ""
        view.endEditing(true)
    }
    
    @objc private func clearText() {
        textView.text = ""
    }
}

// MARK: - UITextViewDelegate
extension KeyboardViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // 实时发送文本变化（可选）
        // 这里可以实现实时输入功能，但可能会导致性能问题
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // 如果输入的是回车键，发送当前文本
        if text == "\n" {
            sendText()
            return false
        }
        return true
    }
}
