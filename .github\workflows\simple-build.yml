name: Simple iOS Build

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Show environment
      run: |
        echo "=== Xcode Version ==="
        xcodebuild -version
        echo ""
        echo "=== Available SDKs ==="
        xcodebuild -showsdks
        echo ""
        echo "=== Project Structure ==="
        ls -la
    
    - name: Validate project
      run: |
        echo "=== Project Info ==="
        xcodebuild -project KeyPhone.xcodeproj -list
    
    - name: Build for iOS Simulator (Test)
      run: |
        echo "=== Building for iOS Simulator ==="
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Debug \
                   -destination 'platform=iOS Simulator,name=iPhone 14' \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   build
    
    - name: Build for iOS Device (Release)
      run: |
        echo "=== Building for iOS Device ==="
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Release \
                   -destination 'generic/platform=iOS' \
                   -derivedDataPath ./DerivedData \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   CODE_SIGN_IDENTITY="" \
                   PROVISIONING_PROFILE="" \
                   build
    
    - name: Find and package app
      run: |
        echo "=== Finding built app ==="
        find ./DerivedData -name "*.app" -type d
        
        APP_PATH=$(find ./DerivedData -name "KeyPhone.app" -type d | head -1)
        echo "Found app at: $APP_PATH"
        
        if [ -n "$APP_PATH" ]; then
          echo "=== Creating IPA ==="
          mkdir -p ./output/Payload
          cp -r "$APP_PATH" ./output/Payload/
          
          cd ./output
          zip -r KeyPhone-unsigned.ipa Payload/
          
          echo "=== IPA Created ==="
          ls -la KeyPhone-unsigned.ipa
          
          # 验证IPA内容
          echo "=== IPA Contents ==="
          unzip -l KeyPhone-unsigned.ipa | head -20
        else
          echo "❌ No app found"
          echo "=== DerivedData Contents ==="
          find ./DerivedData -type f -name "*.app" || echo "No .app files found"
          find ./DerivedData -type d | head -20
          exit 1
        fi
    
    - name: Upload IPA
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-Unsigned-IPA
        path: ./output/KeyPhone-unsigned.ipa
        retention-days: 30
    
    - name: Create installation guide
      run: |
        cat > ./output/INSTALL.md << 'EOF'
        # KeyPhone 安装指南
        
        ## 下载的文件
        - `KeyPhone-unsigned.ipa` - 未签名的iOS应用
        
        ## 安装方法
        
        ### 1. 使用AltStore (推荐)
        1. 下载AltStore: https://altstore.io/
        2. 在电脑上安装AltServer
        3. 在iPhone上安装AltStore应用
        4. 使用AltStore安装KeyPhone.ipa
        
        ### 2. 使用Sideloadly
        1. 下载Sideloadly: https://sideloadly.io/
        2. 连接iPhone到电脑
        3. 拖拽IPA文件到Sideloadly
        4. 输入Apple ID并安装
        
        ### 3. 使用3uTools
        1. 下载3uTools: https://www.3u.com/
        2. 连接iPhone
        3. 使用"安装本地应用"功能
        4. 选择IPA文件安装
        
        ## 注意事项
        - 安装后需要在设置中信任开发者证书
        - 免费Apple ID每7天需要重新安装
        - 确保iPhone运行iOS 13.0或更高版本
        
        ## PC端设置
        1. 进入PC-Receiver目录
        2. 运行install.bat安装依赖
        3. 运行run.bat启动接收程序
        4. 在iPhone上连接PC
        
        构建时间: $(date)
        EOF
    
    - name: Upload installation guide
      uses: actions/upload-artifact@v4
      with:
        name: Installation-Guide
        path: ./output/INSTALL.md
        retention-days: 30
