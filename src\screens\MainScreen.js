import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  StatusBar,
} from 'react-native';
import BluetoothService from '../services/BluetoothService';

const MainScreen = ({navigation}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isScanning, setIsScanning] = useState(false);

  useEffect(() => {
    initializeBluetooth();
    
    const listener = (event, data) => {
      switch (event) {
        case 'connected':
          setIsConnected(true);
          setIsScanning(false);
          Alert.alert('连接成功', '已连接到PC');
          break;
        case 'disconnected':
          setIsConnected(false);
          setIsScanning(false);
          Alert.alert('连接断开', '与PC的连接已断开');
          break;
        case 'stateChange':
          console.log('Bluetooth state:', data);
          break;
      }
    };

    BluetoothService.addListener(listener);

    return () => {
      BluetoothService.removeListener(listener);
      BluetoothService.cleanup();
    };
  }, []);

  const initializeBluetooth = async () => {
    const success = await BluetoothService.initialize();
    if (!success) {
      Alert.alert('错误', '无法初始化蓝牙服务');
    }
  };

  const handleConnect = async () => {
    if (isConnected) {
      const success = await BluetoothService.disconnect();
      if (success) {
        setIsConnected(false);
      }
    } else {
      setIsScanning(true);
      const success = await BluetoothService.startScan();
      if (!success) {
        setIsScanning(false);
        Alert.alert('错误', '无法开始扫描设备');
      }
    }
  };

  const navigateToMouse = () => {
    if (isConnected) {
      navigation.navigate('Mouse');
    } else {
      Alert.alert('提示', '请先连接到PC');
    }
  };

  const navigateToKeyboard = () => {
    if (isConnected) {
      navigation.navigate('Keyboard');
    } else {
      Alert.alert('提示', '请先连接到PC');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#007AFF" />
      
      <View style={styles.header}>
        <Text style={styles.title}>KeyPhone</Text>
        <Text style={styles.subtitle}>蓝牙鼠标键盘控制器</Text>
      </View>

      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>连接状态</Text>
        <Text style={[
          styles.statusText,
          {color: isConnected ? '#4CAF50' : '#F44336'}
        ]}>
          {isConnected ? '已连接' : isScanning ? '扫描中...' : '未连接'}
        </Text>
      </View>

      <TouchableOpacity
        style={[
          styles.connectButton,
          {backgroundColor: isConnected ? '#F44336' : '#007AFF'}
        ]}
        onPress={handleConnect}
        disabled={isScanning}>
        <Text style={styles.connectButtonText}>
          {isConnected ? '断开连接' : isScanning ? '扫描中...' : '连接PC'}
        </Text>
      </TouchableOpacity>

      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[
            styles.controlButton,
            styles.mouseButton,
            {opacity: isConnected ? 1 : 0.5}
          ]}
          onPress={navigateToMouse}
          disabled={!isConnected}>
          <Text style={styles.controlButtonText}>🖱️</Text>
          <Text style={styles.controlButtonLabel}>鼠标控制</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.controlButton,
            styles.keyboardButton,
            {opacity: isConnected ? 1 : 0.5}
          ]}
          onPress={navigateToKeyboard}
          disabled={!isConnected}>
          <Text style={styles.controlButtonText}>⌨️</Text>
          <Text style={styles.controlButtonLabel}>键盘控制</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          确保PC端程序正在运行并且蓝牙已开启
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  statusLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  connectButton: {
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 25,
    alignItems: 'center',
    marginBottom: 40,
  },
  connectButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 40,
  },
  controlButton: {
    width: 120,
    height: 120,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  mouseButton: {
    backgroundColor: '#4CAF50',
  },
  keyboardButton: {
    backgroundColor: '#FF9800',
  },
  controlButtonText: {
    fontSize: 40,
    marginBottom: 8,
  },
  controlButtonLabel: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default MainScreen;
