{"name": "keyphone-rn", "version": "1.0.0", "description": "KeyPhone - Bluetooth Mouse & Keyboard Controller", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build-android": "cd android && ./gradlew assembleRelease", "build-ios": "react-native run-ios --configuration Release"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-ble-manager": "^10.1.2", "react-native-vector-icons": "^10.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}