name: Debug Build

on:
  workflow_dispatch:

jobs:
  debug:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Debug project structure
      run: |
        echo "=== Current Directory ==="
        pwd
        ls -la
        
        echo ""
        echo "=== KeyPhone.xcodeproj Contents ==="
        ls -la KeyPhone.xcodeproj/
        
        echo ""
        echo "=== Schemes Directory ==="
        ls -la KeyPhone.xcodeproj/xcshareddata/ || echo "xcshareddata not found"
        ls -la KeyPhone.xcodeproj/xcshareddata/xcschemes/ || echo "xcschemes not found"
        
        echo ""
        echo "=== Project File Check ==="
        file KeyPhone.xcodeproj/project.pbxproj
        head -5 KeyPhone.xcodeproj/project.pbxproj
    
    - name: Test xcodebuild list
      run: |
        echo "=== Testing xcodebuild -list ==="
        xcodebuild -project KeyPhone.xcodeproj -list || echo "Failed to list project"
    
    - name: Test simple build command
      run: |
        echo "=== Testing simple build ==="
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Debug \
                   -destination 'platform=iOS Simulator,name=iPhone 14' \
                   -dry-run \
                   build || echo "Dry run failed"
    
    - name: Check for workspace
      run: |
        echo "=== Checking for workspace files ==="
        find . -name "*.xcworkspace" -type d || echo "No workspace found"
        
        echo ""
        echo "=== All Xcode files ==="
        find . -name "*.xc*" -type d
