name: Test Build

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  test-build:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Show Xcode version
      run: xcodebuild -version
    
    - name: Verify project structure
      run: |
        echo "=== Project Structure ==="
        ls -la
        echo ""
        echo "=== KeyPhone Directory ==="
        ls -la KeyPhone/
        echo ""
        echo "=== Xcode Project ==="
        ls -la KeyPhone.xcodeproj/
    
    - name: List project info
      run: |
        echo "=== Project Info ==="
        xcodebuild -project KeyPhone.xcodeproj -list
    
    - name: Test build (compile only)
      run: |
        echo "=== Testing Build ==="
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Debug \
                   -destination 'platform=iOS Simulator,name=iPhone 14' \
                   -derivedDataPath ./DerivedData \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   CODE_SIGN_IDENTITY="" \
                   PROVISIONING_PROFILE="" \
                   build
    
    - name: Show build results
      run: |
        echo "=== Build Results ==="
        find ./DerivedData -name "*.app" -type d | head -5
        echo ""
        echo "=== DerivedData Structure ==="
        ls -la ./DerivedData/ || echo "DerivedData not found"
