name: Build Simple KeyPhone App

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-web-app:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: npm install
    
    - name: Create Web App
      run: |
        # 创建一个简单的Web版本KeyPhone应用
        mkdir -p web-app
        
        cat > web-app/index.html << 'EOF'
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>KeyPhone - Web版蓝牙鼠标键盘控制器</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 20px;
                    color: white;
                }
                
                .container {
                    max-width: 400px;
                    width: 100%;
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    border-radius: 20px;
                    padding: 30px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                }
                
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .title {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                
                .subtitle {
                    font-size: 16px;
                    opacity: 0.8;
                }
                
                .status {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .status-text {
                    font-size: 18px;
                    font-weight: 600;
                    padding: 10px 20px;
                    border-radius: 25px;
                    background: rgba(255, 255, 255, 0.2);
                    display: inline-block;
                }
                
                .connect-btn {
                    width: 100%;
                    padding: 15px;
                    font-size: 18px;
                    font-weight: 600;
                    border: none;
                    border-radius: 25px;
                    background: #4CAF50;
                    color: white;
                    cursor: pointer;
                    margin-bottom: 30px;
                    transition: all 0.3s ease;
                }
                
                .connect-btn:hover {
                    background: #45a049;
                    transform: translateY(-2px);
                }
                
                .connect-btn:disabled {
                    background: #666;
                    cursor: not-allowed;
                    transform: none;
                }
                
                .controls {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-bottom: 30px;
                }
                
                .control-btn {
                    padding: 20px;
                    border: none;
                    border-radius: 15px;
                    font-size: 16px;
                    font-weight: 600;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                }
                
                .control-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .mouse-btn {
                    background: #2196F3;
                }
                
                .mouse-btn:hover:not(:disabled) {
                    background: #1976D2;
                    transform: translateY(-2px);
                }
                
                .keyboard-btn {
                    background: #FF9800;
                }
                
                .keyboard-btn:hover:not(:disabled) {
                    background: #F57C00;
                    transform: translateY(-2px);
                }
                
                .icon {
                    font-size: 24px;
                }
                
                .touchpad {
                    width: 100%;
                    height: 200px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 15px;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    opacity: 0.7;
                    cursor: pointer;
                    user-select: none;
                    touch-action: none;
                }
                
                .mouse-controls {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 20px;
                }
                
                .mouse-control-btn {
                    padding: 15px;
                    border: none;
                    border-radius: 10px;
                    font-size: 14px;
                    font-weight: 600;
                    color: white;
                    cursor: pointer;
                }
                
                .left-click {
                    background: #4CAF50;
                }
                
                .right-click {
                    background: #FF5722;
                }
                
                .text-input {
                    width: 100%;
                    padding: 15px;
                    border: none;
                    border-radius: 10px;
                    font-size: 16px;
                    margin-bottom: 15px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #333;
                }
                
                .send-btn {
                    width: 100%;
                    padding: 12px;
                    border: none;
                    border-radius: 10px;
                    background: #2196F3;
                    color: white;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                }
                
                .hidden {
                    display: none;
                }
                
                .footer {
                    text-align: center;
                    margin-top: 20px;
                    font-size: 14px;
                    opacity: 0.7;
                }
                
                .warning {
                    background: rgba(255, 193, 7, 0.2);
                    border: 1px solid rgba(255, 193, 7, 0.5);
                    border-radius: 10px;
                    padding: 15px;
                    margin-bottom: 20px;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">KeyPhone</div>
                    <div class="subtitle">蓝牙鼠标键盘控制器</div>
                </div>
                
                <div class="warning">
                    ⚠️ Web版本仅供演示，实际功能需要下载移动应用
                </div>
                
                <div class="status">
                    <div class="status-text" id="status">未连接</div>
                </div>
                
                <button class="connect-btn" id="connectBtn" onclick="toggleConnection()">
                    连接PC
                </button>
                
                <div class="controls">
                    <button class="control-btn mouse-btn" id="mouseBtn" onclick="showMouseControl()" disabled>
                        <div class="icon">🖱️</div>
                        <div>鼠标控制</div>
                    </button>
                    <button class="control-btn keyboard-btn" id="keyboardBtn" onclick="showKeyboardControl()" disabled>
                        <div class="icon">⌨️</div>
                        <div>键盘控制</div>
                    </button>
                </div>
                
                <!-- 鼠标控制界面 -->
                <div id="mouseControl" class="hidden">
                    <div class="touchpad" id="touchpad">
                        触摸板区域<br>
                        <small>滑动移动鼠标，点击执行左键</small>
                    </div>
                    <div class="mouse-controls">
                        <button class="mouse-control-btn left-click" onclick="simulateClick('left')">左键</button>
                        <button class="mouse-control-btn right-click" onclick="simulateClick('right')">右键</button>
                    </div>
                    <button class="connect-btn" onclick="showMainControl()">返回主界面</button>
                </div>
                
                <!-- 键盘控制界面 -->
                <div id="keyboardControl" class="hidden">
                    <input type="text" class="text-input" id="textInput" placeholder="输入要发送的文本...">
                    <button class="send-btn" onclick="sendText()">发送文本</button>
                    <button class="connect-btn" onclick="showMainControl()">返回主界面</button>
                </div>
                
                <div class="footer">
                    请下载移动应用以获得完整功能<br>
                    支持Android和iOS
                </div>
            </div>
            
            <script>
                let isConnected = false;
                let currentView = 'main';
                
                function toggleConnection() {
                    const btn = document.getElementById('connectBtn');
                    const status = document.getElementById('status');
                    const mouseBtn = document.getElementById('mouseBtn');
                    const keyboardBtn = document.getElementById('keyboardBtn');
                    
                    if (!isConnected) {
                        // 模拟连接过程
                        btn.textContent = '连接中...';
                        btn.disabled = true;
                        
                        setTimeout(() => {
                            isConnected = true;
                            status.textContent = '已连接 (演示模式)';
                            status.style.background = 'rgba(76, 175, 80, 0.3)';
                            btn.textContent = '断开连接';
                            btn.style.background = '#F44336';
                            btn.disabled = false;
                            mouseBtn.disabled = false;
                            keyboardBtn.disabled = false;
                        }, 2000);
                    } else {
                        isConnected = false;
                        status.textContent = '未连接';
                        status.style.background = 'rgba(255, 255, 255, 0.2)';
                        btn.textContent = '连接PC';
                        btn.style.background = '#4CAF50';
                        mouseBtn.disabled = true;
                        keyboardBtn.disabled = true;
                        showMainControl();
                    }
                }
                
                function showMouseControl() {
                    if (!isConnected) return;
                    document.getElementById('mouseControl').classList.remove('hidden');
                    document.querySelector('.controls').classList.add('hidden');
                    currentView = 'mouse';
                    setupTouchpad();
                }
                
                function showKeyboardControl() {
                    if (!isConnected) return;
                    document.getElementById('keyboardControl').classList.remove('hidden');
                    document.querySelector('.controls').classList.add('hidden');
                    currentView = 'keyboard';
                }
                
                function showMainControl() {
                    document.getElementById('mouseControl').classList.add('hidden');
                    document.getElementById('keyboardControl').classList.add('hidden');
                    document.querySelector('.controls').classList.remove('hidden');
                    currentView = 'main';
                }
                
                function setupTouchpad() {
                    const touchpad = document.getElementById('touchpad');
                    let isTracking = false;
                    let lastX = 0;
                    let lastY = 0;
                    
                    touchpad.addEventListener('mousedown', (e) => {
                        isTracking = true;
                        lastX = e.clientX;
                        lastY = e.clientY;
                        e.preventDefault();
                    });
                    
                    touchpad.addEventListener('mousemove', (e) => {
                        if (!isTracking) return;
                        
                        const deltaX = e.clientX - lastX;
                        const deltaY = e.clientY - lastY;
                        
                        // 模拟鼠标移动
                        console.log(`Mouse move: ${deltaX}, ${deltaY}`);
                        
                        lastX = e.clientX;
                        lastY = e.clientY;
                        e.preventDefault();
                    });
                    
                    touchpad.addEventListener('mouseup', () => {
                        isTracking = false;
                    });
                    
                    touchpad.addEventListener('click', () => {
                        simulateClick('left');
                    });
                    
                    // 触摸事件支持
                    touchpad.addEventListener('touchstart', (e) => {
                        isTracking = true;
                        const touch = e.touches[0];
                        lastX = touch.clientX;
                        lastY = touch.clientY;
                        e.preventDefault();
                    });
                    
                    touchpad.addEventListener('touchmove', (e) => {
                        if (!isTracking) return;
                        
                        const touch = e.touches[0];
                        const deltaX = touch.clientX - lastX;
                        const deltaY = touch.clientY - lastY;
                        
                        console.log(`Touch move: ${deltaX}, ${deltaY}`);
                        
                        lastX = touch.clientX;
                        lastY = touch.clientY;
                        e.preventDefault();
                    });
                    
                    touchpad.addEventListener('touchend', () => {
                        isTracking = false;
                    });
                }
                
                function simulateClick(button) {
                    console.log(`${button} click`);
                    // 这里会发送到PC端
                }
                
                function sendText() {
                    const input = document.getElementById('textInput');
                    const text = input.value.trim();
                    
                    if (text) {
                        console.log(`Send text: ${text}`);
                        input.value = '';
                        // 这里会发送到PC端
                    }
                }
                
                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (currentView === 'keyboard' && e.key === 'Enter') {
                        sendText();
                    }
                });
            </script>
        </body>
        </html>
        EOF
        
        echo "✅ Web应用创建完成"
    
    - name: Create Android APK Info
      run: |
        cat > web-app/android-info.html << 'EOF'
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>KeyPhone Android APK 下载</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0;
                    padding: 20px;
                    color: white;
                }
                .container {
                    max-width: 500px;
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    border-radius: 20px;
                    padding: 40px;
                    text-align: center;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                }
                .title {
                    font-size: 32px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .subtitle {
                    font-size: 18px;
                    margin-bottom: 30px;
                    opacity: 0.9;
                }
                .download-btn {
                    display: inline-block;
                    padding: 15px 30px;
                    background: #4CAF50;
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-size: 18px;
                    font-weight: 600;
                    margin: 10px;
                    transition: all 0.3s ease;
                }
                .download-btn:hover {
                    background: #45a049;
                    transform: translateY(-2px);
                }
                .info {
                    margin-top: 30px;
                    padding: 20px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 15px;
                    text-align: left;
                }
                .step {
                    margin-bottom: 15px;
                    padding-left: 20px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="title">📱 KeyPhone</div>
                <div class="subtitle">蓝牙鼠标键盘控制器</div>
                
                <a href="#" class="download-btn">📥 下载 Android APK</a>
                <a href="#" class="download-btn">📥 下载 iOS IPA</a>
                
                <div class="info">
                    <h3>📋 安装步骤:</h3>
                    <div class="step">1. 下载对应平台的安装包</div>
                    <div class="step">2. Android: 启用"未知来源"后安装APK</div>
                    <div class="step">3. iOS: 使用AltStore或Sideloadly安装IPA</div>
                    <div class="step">4. 在PC上运行接收程序</div>
                    <div class="step">5. 打开手机应用连接PC</div>
                </div>
                
                <div class="info">
                    <h3>💻 PC端设置:</h3>
                    <div class="step">1. 下载项目源码</div>
                    <div class="step">2. 进入PC-Receiver目录</div>
                    <div class="step">3. 运行install.bat安装依赖</div>
                    <div class="step">4. 运行run.bat启动程序</div>
                </div>
            </div>
        </body>
        </html>
        EOF
    
    - name: Upload Web App
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-Web-Demo
        path: web-app/
        retention-days: 30
    
    - name: Create Installation Package
      run: |
        mkdir -p release-package
        
        # 复制所有必要文件
        cp -r PC-Receiver release-package/
        cp -r web-app release-package/
        cp README.md release-package/
        cp quick-start.md release-package/
        cp TROUBLESHOOTING.md release-package/
        
        # 创建安装指南
        cat > release-package/INSTALL.md << 'EOF'
        # KeyPhone 完整安装包
        
        ## 📦 包含内容
        - PC-Receiver/ - PC端接收程序
        - web-app/ - Web演示版本
        - 完整文档和指南
        
        ## 🚀 快速开始
        
        ### 1. 设置PC端
        ```bash
        cd PC-Receiver
        install.bat  # Windows
        run.bat      # 启动程序
        ```
        
        ### 2. 获取移动应用
        - 访问GitHub仓库的Actions页面
        - 下载最新的APK或IPA文件
        - 按照安装指南安装到手机
        
        ### 3. 连接使用
        - 确保PC和手机蓝牙开启
        - 在手机上打开KeyPhone应用
        - 点击"连接PC"
        - 开始使用鼠标键盘功能
        
        ## 📚 更多信息
        - README.md - 完整说明文档
        - quick-start.md - 快速启动指南
        - TROUBLESHOOTING.md - 故障排除
        EOF
        
        # 创建压缩包
        cd release-package
        zip -r ../KeyPhone-Complete-Package.zip .
        cd ..
    
    - name: Upload Complete Package
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-Complete-Package
        path: KeyPhone-Complete-Package.zip
        retention-days: 30
