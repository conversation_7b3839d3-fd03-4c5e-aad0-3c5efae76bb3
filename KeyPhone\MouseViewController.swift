import UIKit

class MouseViewController: UIViewController {
    
    private let bluetoothManager = BluetoothManager.shared
    private var touchpadView: UIView!
    private var leftClickButton: UIButton!
    private var rightClickButton: UIButton!
    private var scrollView: UIView!
    
    private var lastTouchPoint: CGPoint = .zero
    private var sensitivity: Float = 2.0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupGestures()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "鼠标控制"
        
        // 添加设置按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "设置",
            style: .plain,
            target: self,
            action: #selector(settingsButtonTapped)
        )
        
        // 触摸板区域
        touchpadView = UIView()
        touchpadView.backgroundColor = .systemGray6
        touchpadView.layer.cornerRadius = 12
        touchpadView.layer.borderWidth = 1
        touchpadView.layer.borderColor = UIColor.systemGray4.cgColor
        touchpadView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(touchpadView)
        
        // 添加触摸板标签
        let touchpadLabel = UILabel()
        touchpadLabel.text = "触摸板区域"
        touchpadLabel.textAlignment = .center
        touchpadLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        touchpadLabel.textColor = .systemGray
        touchpadLabel.translatesAutoresizingMaskIntoConstraints = false
        touchpadView.addSubview(touchpadLabel)
        
        // 左键按钮
        leftClickButton = UIButton(type: .system)
        leftClickButton.setTitle("左键", for: .normal)
        leftClickButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        leftClickButton.backgroundColor = .systemBlue
        leftClickButton.setTitleColor(.white, for: .normal)
        leftClickButton.layer.cornerRadius = 8
        leftClickButton.translatesAutoresizingMaskIntoConstraints = false
        leftClickButton.addTarget(self, action: #selector(leftClickPressed), for: .touchDown)
        leftClickButton.addTarget(self, action: #selector(leftClickReleased), for: [.touchUpInside, .touchUpOutside])
        view.addSubview(leftClickButton)
        
        // 右键按钮
        rightClickButton = UIButton(type: .system)
        rightClickButton.setTitle("右键", for: .normal)
        rightClickButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        rightClickButton.backgroundColor = .systemOrange
        rightClickButton.setTitleColor(.white, for: .normal)
        rightClickButton.layer.cornerRadius = 8
        rightClickButton.translatesAutoresizingMaskIntoConstraints = false
        rightClickButton.addTarget(self, action: #selector(rightClickPressed), for: .touchDown)
        rightClickButton.addTarget(self, action: #selector(rightClickReleased), for: [.touchUpInside, .touchUpOutside])
        view.addSubview(rightClickButton)
        
        // 滚轮区域
        scrollView = UIView()
        scrollView.backgroundColor = .systemGray5
        scrollView.layer.cornerRadius = 8
        scrollView.layer.borderWidth = 1
        scrollView.layer.borderColor = UIColor.systemGray4.cgColor
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 滚轮标签
        let scrollLabel = UILabel()
        scrollLabel.text = "滚轮"
        scrollLabel.textAlignment = .center
        scrollLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        scrollLabel.textColor = .systemGray
        scrollLabel.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(scrollLabel)
        
        // 设置约束
        NSLayoutConstraint.activate([
            // 触摸板
            touchpadView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            touchpadView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            touchpadView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            touchpadView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.5),
            
            // 触摸板标签
            touchpadLabel.centerXAnchor.constraint(equalTo: touchpadView.centerXAnchor),
            touchpadLabel.centerYAnchor.constraint(equalTo: touchpadView.centerYAnchor),
            
            // 左键按钮
            leftClickButton.topAnchor.constraint(equalTo: touchpadView.bottomAnchor, constant: 20),
            leftClickButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            leftClickButton.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.4),
            leftClickButton.heightAnchor.constraint(equalToConstant: 60),
            
            // 右键按钮
            rightClickButton.topAnchor.constraint(equalTo: touchpadView.bottomAnchor, constant: 20),
            rightClickButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            rightClickButton.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.4),
            rightClickButton.heightAnchor.constraint(equalToConstant: 60),
            
            // 滚轮区域
            scrollView.topAnchor.constraint(equalTo: leftClickButton.bottomAnchor, constant: 20),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            scrollView.heightAnchor.constraint(equalToConstant: 80),
            
            // 滚轮标签
            scrollLabel.centerXAnchor.constraint(equalTo: scrollView.centerXAnchor),
            scrollLabel.centerYAnchor.constraint(equalTo: scrollView.centerYAnchor)
        ])
    }
    
    private func setupGestures() {
        // 触摸板手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleTouchpadPan(_:)))
        touchpadView.addGestureRecognizer(panGesture)
        
        // 触摸板点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTouchpadTap(_:)))
        touchpadView.addGestureRecognizer(tapGesture)
        
        // 滚轮手势
        let scrollPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleScrollPan(_:)))
        scrollView.addGestureRecognizer(scrollPanGesture)
    }
    
    @objc private func handleTouchpadPan(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: touchpadView)
        
        if gesture.state == .began {
            lastTouchPoint = gesture.location(in: touchpadView)
        } else if gesture.state == .changed {
            let deltaX = Float(translation.x) * sensitivity
            let deltaY = Float(translation.y) * sensitivity
            
            bluetoothManager.sendMouseData(deltaX: deltaX, deltaY: deltaY)
            gesture.setTranslation(.zero, in: touchpadView)
        }
    }
    
    @objc private func handleTouchpadTap(_ gesture: UITapGestureRecognizer) {
        // 单击触摸板执行左键点击
        bluetoothManager.sendMouseData(deltaX: 0, deltaY: 0, leftClick: true)
        
        // 添加视觉反馈
        UIView.animate(withDuration: 0.1, animations: {
            self.touchpadView.backgroundColor = .systemGray4
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.touchpadView.backgroundColor = .systemGray6
            }
        }
    }
    
    @objc private func handleScrollPan(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: scrollView)
        
        if gesture.state == .changed {
            let scrollDelta = Float(-translation.y) * 0.1 // 负号是为了让滚动方向更自然
            bluetoothManager.sendMouseData(deltaX: 0, deltaY: 0, scroll: scrollDelta)
            gesture.setTranslation(.zero, in: scrollView)
        }
    }
    
    @objc private func leftClickPressed() {
        bluetoothManager.sendMouseData(deltaX: 0, deltaY: 0, leftClick: true)
        leftClickButton.backgroundColor = .systemBlue.withAlphaComponent(0.7)
    }
    
    @objc private func leftClickReleased() {
        leftClickButton.backgroundColor = .systemBlue
    }
    
    @objc private func rightClickPressed() {
        bluetoothManager.sendMouseData(deltaX: 0, deltaY: 0, rightClick: true)
        rightClickButton.backgroundColor = .systemOrange.withAlphaComponent(0.7)
    }
    
    @objc private func rightClickReleased() {
        rightClickButton.backgroundColor = .systemOrange
    }
    
    @objc private func settingsButtonTapped() {
        let alert = UIAlertController(title: "鼠标设置", message: "调整鼠标灵敏度", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "灵敏度 (0.1 - 5.0)"
            textField.text = "\(self.sensitivity)"
            textField.keyboardType = .decimalPad
        }
        
        let saveAction = UIAlertAction(title: "保存", style: .default) { _ in
            if let text = alert.textFields?.first?.text,
               let newSensitivity = Float(text),
               newSensitivity >= 0.1 && newSensitivity <= 5.0 {
                self.sensitivity = newSensitivity
            }
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        
        alert.addAction(saveAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
}
