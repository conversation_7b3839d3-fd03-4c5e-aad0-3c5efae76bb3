# KeyPhone 快速启动指南

## 🎯 目标
将你的手机变成PC的无线鼠标和键盘

## 🚀 最快方式 (5分钟搞定)

### 步骤1: 获取移动应用 (2分钟)

#### 选项A: 直接下载 (推荐)
1. 访问本项目的GitHub Actions页面
2. 下载最新构建的文件：
   - **Android用户**: 下载 `KeyPhone-Android-APK`
   - **iPhone用户**: 下载 `KeyPhone-iOS-IPA`

#### 选项B: 自动构建
1. Fork这个仓库到你的GitHub账户
2. GitHub会自动构建应用文件
3. 在你的仓库Actions页面下载

### 步骤2: 安装移动应用 (1分钟)

#### Android安装
1. 在手机设置中启用"未知来源"安装
2. 直接安装下载的APK文件

#### iPhone安装
1. 下载AltStore: https://altstore.io/
2. 按照AltStore指南设置
3. 使用AltStore安装IPA文件

### 步骤3: 设置PC端 (2分钟)

1. **下载项目文件**:
   ```bash
   git clone https://github.com/你的用户名/keyphone.git
   cd keyphone/PC-Receiver
   ```

2. **安装依赖** (Windows):
   ```bash
   # 双击运行
   install.bat
   ```

3. **启动接收程序**:
   ```bash
   # 双击运行
   run.bat
   ```

### 步骤4: 连接使用 (30秒)

1. **确保蓝牙开启**: PC和手机都要开启蓝牙
2. **打开手机应用**: 启动KeyPhone应用
3. **点击连接**: 点击"连接PC"按钮
4. **开始使用**: 连接成功后即可使用鼠标和键盘功能

## 🎮 功能说明

### 鼠标控制
- **移动鼠标**: 在触摸板区域滑动手指
- **左键点击**: 点击触摸板或按左键按钮
- **右键点击**: 按右键按钮
- **滚轮**: 在滚轮区域上下滑动
- **灵敏度调节**: 点击设置按钮调整

### 键盘控制
- **文本输入**: 在文本框输入后点击发送
- **特殊按键**: Enter、Backspace、Tab等
- **方向键**: 上下左右箭头
- **功能键**: F1-F12
- **修饰键**: Ctrl、Alt、Cmd、Shift

## 🔧 故障排除

### 连接不上？
1. 确认PC端程序正在运行
2. 检查蓝牙是否开启
3. 重启蓝牙服务
4. 确保设备距离在10米内

### 应用安装失败？
- **Android**: 检查是否允许未知来源安装
- **iPhone**: 确保AltStore设置正确，检查证书是否信任

### 操作没反应？
1. 检查PC端程序日志
2. 确认连接状态为"已连接"
3. 重新连接设备

## 📞 获取帮助

- **GitHub Issues**: 提交问题和建议
- **文档**: 查看完整的README.md
- **故障排除**: 查看TROUBLESHOOTING.md

## 🎉 享受使用！

现在你可以躺在沙发上用手机控制电脑了！
