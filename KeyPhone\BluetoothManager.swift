import Foundation
import CoreBluetooth

protocol BluetoothManagerDelegate: AnyObject {
    func bluetoothManagerDidConnect()
    func bluetoothManagerDidDisconnect()
    func bluetoothManagerDidFailToConnect(error: Error)
}

class BluetoothManager: NSObject {
    static let shared = BluetoothManager()
    
    weak var delegate: BluetoothManagerDelegate?
    
    private var centralManager: CBCentralManager!
    private var connectedPeripheral: CBPeripheral?
    private var writeCharacteristic: CBCharacteristic?
    
    // 自定义服务和特征UUID
    private let serviceUUID = CBUUID(string: "12345678-1234-1234-1234-123456789ABC")
    private let characteristicUUID = CBUUID(string: "*************-4321-4321-CBA987654321")
    
    var isConnected: Bool {
        return connectedPeripheral?.state == .connected
    }
    
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    func startScanning() {
        guard centralManager.state == .poweredOn else {
            print("Bluetooth is not powered on")
            return
        }
        
        centralManager.scanForPeripherals(withServices: [serviceUUID], options: nil)
        print("Started scanning for peripherals...")
    }
    
    func stopScanning() {
        centralManager.stopScan()
        print("Stopped scanning")
    }
    
    func disconnect() {
        guard let peripheral = connectedPeripheral else { return }
        centralManager.cancelPeripheralConnection(peripheral)
    }
    
    func sendMouseData(deltaX: Float, deltaY: Float, leftClick: Bool = false, rightClick: Bool = false, scroll: Float = 0) {
        guard let characteristic = writeCharacteristic else { return }
        
        let command = MouseCommand(
            type: .mouse,
            deltaX: deltaX,
            deltaY: deltaY,
            leftClick: leftClick,
            rightClick: rightClick,
            scroll: scroll
        )
        
        if let data = try? JSONEncoder().encode(command) {
            connectedPeripheral?.writeValue(data, for: characteristic, type: .withResponse)
        }
    }
    
    func sendKeyboardData(text: String) {
        guard let characteristic = writeCharacteristic else { return }
        
        let command = KeyboardCommand(
            type: .keyboard,
            text: text
        )
        
        if let data = try? JSONEncoder().encode(command) {
            connectedPeripheral?.writeValue(data, for: characteristic, type: .withResponse)
        }
    }
    
    func sendSpecialKey(_ key: SpecialKey) {
        guard let characteristic = writeCharacteristic else { return }
        
        let command = SpecialKeyCommand(
            type: .specialKey,
            key: key
        )
        
        if let data = try? JSONEncoder().encode(command) {
            connectedPeripheral?.writeValue(data, for: characteristic, type: .withResponse)
        }
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            print("Bluetooth is powered on")
        case .poweredOff:
            print("Bluetooth is powered off")
        case .resetting:
            print("Bluetooth is resetting")
        case .unauthorized:
            print("Bluetooth is unauthorized")
        case .unsupported:
            print("Bluetooth is unsupported")
        case .unknown:
            print("Bluetooth state is unknown")
        @unknown default:
            print("Unknown Bluetooth state")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        print("Discovered peripheral: \(peripheral.name ?? "Unknown")")
        
        // 连接到第一个发现的设备
        connectedPeripheral = peripheral
        peripheral.delegate = self
        centralManager.connect(peripheral, options: nil)
        stopScanning()
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        print("Connected to peripheral: \(peripheral.name ?? "Unknown")")
        peripheral.discoverServices([serviceUUID])
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("Failed to connect to peripheral: \(error?.localizedDescription ?? "Unknown error")")
        delegate?.bluetoothManagerDidFailToConnect(error: error ?? BluetoothError.connectionFailed)
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        print("Disconnected from peripheral")
        connectedPeripheral = nil
        writeCharacteristic = nil
        delegate?.bluetoothManagerDidDisconnect()
    }
}

// MARK: - CBPeripheralDelegate
extension BluetoothManager: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        guard error == nil else {
            print("Error discovering services: \(error!.localizedDescription)")
            return
        }
        
        guard let services = peripheral.services else { return }
        
        for service in services {
            if service.uuid == serviceUUID {
                peripheral.discoverCharacteristics([characteristicUUID], for: service)
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        guard error == nil else {
            print("Error discovering characteristics: \(error!.localizedDescription)")
            return
        }
        
        guard let characteristics = service.characteristics else { return }
        
        for characteristic in characteristics {
            if characteristic.uuid == characteristicUUID {
                writeCharacteristic = characteristic
                delegate?.bluetoothManagerDidConnect()
                print("Found write characteristic")
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("Error writing value: \(error.localizedDescription)")
        }
    }
}

// MARK: - Data Models
enum CommandType: String, Codable {
    case mouse = "mouse"
    case keyboard = "keyboard"
    case specialKey = "specialKey"
}

struct MouseCommand: Codable {
    let type: CommandType
    let deltaX: Float
    let deltaY: Float
    let leftClick: Bool
    let rightClick: Bool
    let scroll: Float
}

struct KeyboardCommand: Codable {
    let type: CommandType
    let text: String
}

enum SpecialKey: String, Codable {
    case enter = "enter"
    case backspace = "backspace"
    case delete = "delete"
    case tab = "tab"
    case escape = "escape"
    case space = "space"
    case leftArrow = "leftArrow"
    case rightArrow = "rightArrow"
    case upArrow = "upArrow"
    case downArrow = "downArrow"
    case home = "home"
    case end = "end"
    case pageUp = "pageUp"
    case pageDown = "pageDown"
    case f1 = "f1"
    case f2 = "f2"
    case f3 = "f3"
    case f4 = "f4"
    case f5 = "f5"
    case f6 = "f6"
    case f7 = "f7"
    case f8 = "f8"
    case f9 = "f9"
    case f10 = "f10"
    case f11 = "f11"
    case f12 = "f12"
    case cmd = "cmd"
    case ctrl = "ctrl"
    case alt = "alt"
    case shift = "shift"
}

struct SpecialKeyCommand: Codable {
    let type: CommandType
    let key: SpecialKey
}

enum BluetoothError: Error {
    case connectionFailed
    case characteristicNotFound
    case writeError
    
    var localizedDescription: String {
        switch self {
        case .connectionFailed:
            return "连接失败"
        case .characteristicNotFound:
            return "未找到特征值"
        case .writeError:
            return "写入数据失败"
        }
    }
}
