import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  PanGestureHandler,
  State,
  Alert,
} from 'react-native';
import {PanGestureHandler} from 'react-native-gesture-handler';
import BluetoothService from '../services/BluetoothService';

const MouseScreen = () => {
  const [sensitivity, setSensitivity] = useState(2.0);

  const handleTouchpadGesture = (event) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const {translationX, translationY} = event.nativeEvent;
      const deltaX = translationX * sensitivity;
      const deltaY = translationY * sensitivity;
      
      BluetoothService.sendMouseData(deltaX, deltaY);
    }
  };

  const handleTouchpadTap = () => {
    BluetoothService.sendMouseData(0, 0, true, false, 0);
  };

  const handleLeftClick = () => {
    BluetoothService.sendMouseData(0, 0, true, false, 0);
  };

  const handleRightClick = () => {
    BluetoothService.sendMouseData(0, 0, false, true, 0);
  };

  const handleScroll = (direction) => {
    const scrollValue = direction === 'up' ? 3 : -3;
    BluetoothService.sendMouseData(0, 0, false, false, scrollValue);
  };

  const showSensitivitySettings = () => {
    Alert.alert(
      '鼠标灵敏度',
      '当前灵敏度: ' + sensitivity.toFixed(1),
      [
        {text: '低 (1.0)', onPress: () => setSensitivity(1.0)},
        {text: '中 (2.0)', onPress: () => setSensitivity(2.0)},
        {text: '高 (3.0)', onPress: () => setSensitivity(3.0)},
        {text: '取消', style: 'cancel'},
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>鼠标控制</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={showSensitivitySettings}>
          <Text style={styles.settingsButtonText}>设置</Text>
        </TouchableOpacity>
      </View>

      <PanGestureHandler onGestureEvent={handleTouchpadGesture}>
        <View style={styles.touchpad}>
          <TouchableOpacity
            style={styles.touchpadArea}
            onPress={handleTouchpadTap}
            activeOpacity={0.8}>
            <Text style={styles.touchpadLabel}>触摸板区域</Text>
            <Text style={styles.touchpadHint}>滑动移动鼠标，点击执行左键</Text>
          </TouchableOpacity>
        </View>
      </PanGestureHandler>

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.clickButton, styles.leftClickButton]}
          onPress={handleLeftClick}>
          <Text style={styles.clickButtonText}>左键</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clickButton, styles.rightClickButton]}
          onPress={handleRightClick}>
          <Text style={styles.clickButtonText}>右键</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.scrollContainer}>
        <Text style={styles.scrollLabel}>滚轮</Text>
        <View style={styles.scrollButtons}>
          <TouchableOpacity
            style={styles.scrollButton}
            onPress={() => handleScroll('up')}>
            <Text style={styles.scrollButtonText}>↑</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.scrollButton}
            onPress={() => handleScroll('down')}>
            <Text style={styles.scrollButtonText}>↓</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          灵敏度: {sensitivity.toFixed(1)} | 点击设置调整
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  settingsButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
  },
  settingsButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  touchpad: {
    flex: 1,
    marginBottom: 20,
  },
  touchpadArea: {
    flex: 1,
    backgroundColor: '#E8E8E8',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#DDD',
  },
  touchpadLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 8,
  },
  touchpadHint: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  clickButton: {
    flex: 1,
    paddingVertical: 20,
    borderRadius: 10,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  leftClickButton: {
    backgroundColor: '#4CAF50',
  },
  rightClickButton: {
    backgroundColor: '#FF9800',
  },
  clickButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  scrollLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 10,
  },
  scrollButtons: {
    flexDirection: 'row',
  },
  scrollButton: {
    backgroundColor: '#9C27B0',
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  scrollButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#999',
  },
});

export default MouseScreen;
