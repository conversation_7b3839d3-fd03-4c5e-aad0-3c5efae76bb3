#!/bin/bash

# KeyPhone IPA 构建脚本
# 用于在Mac上编译生成IPA文件

echo "KeyPhone IPA 构建脚本"
echo "===================="

# 检查Xcode是否安装
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: 未找到Xcode，请先安装Xcode"
    exit 1
fi

# 项目配置
PROJECT_NAME="KeyPhone"
SCHEME_NAME="KeyPhone"
CONFIGURATION="Release"
ARCHIVE_PATH="./build/${PROJECT_NAME}.xcarchive"
EXPORT_PATH="./build"
IPA_NAME="${PROJECT_NAME}.ipa"

# 创建构建目录
mkdir -p build

echo "✅ 开始构建过程..."

# 步骤1: 清理之前的构建
echo ""
echo "🧹 清理之前的构建..."
xcodebuild clean -project ${PROJECT_NAME}.xcodeproj -scheme ${SCHEME_NAME} -configuration ${CONFIGURATION}

# 步骤2: 构建Archive
echo ""
echo "🔨 构建Archive..."
xcodebuild archive \
    -project ${PROJECT_NAME}.xcodeproj \
    -scheme ${SCHEME_NAME} \
    -configuration ${CONFIGURATION} \
    -destination 'generic/platform=iOS' \
    -archivePath ${ARCHIVE_PATH} \
    DEVELOPMENT_TEAM="" \
    CODE_SIGN_IDENTITY="" \
    CODE_SIGNING_REQUIRED=NO \
    CODE_SIGNING_ALLOWED=NO

if [ $? -ne 0 ]; then
    echo "❌ Archive构建失败"
    exit 1
fi

echo "✅ Archive构建成功"

# 步骤3: 创建导出配置文件
echo ""
echo "📝 创建导出配置..."

cat > ${EXPORT_PATH}/ExportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>development</string>
    <key>compileBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <false/>
    <key>signingStyle</key>
    <string>manual</string>
    <key>stripSwiftSymbols</key>
    <true/>
</dict>
</plist>
EOF

# 步骤4: 导出IPA
echo ""
echo "📦 导出IPA文件..."
xcodebuild -exportArchive \
    -archivePath ${ARCHIVE_PATH} \
    -exportPath ${EXPORT_PATH} \
    -exportOptionsPlist ${EXPORT_PATH}/ExportOptions.plist

if [ $? -ne 0 ]; then
    echo "❌ IPA导出失败"
    exit 1
fi

# 查找生成的IPA文件
IPA_FILE=$(find ${EXPORT_PATH} -name "*.ipa" | head -1)

if [ -f "$IPA_FILE" ]; then
    echo "✅ IPA文件生成成功!"
    echo "📍 文件位置: $IPA_FILE"
    echo "📊 文件大小: $(du -h "$IPA_FILE" | cut -f1)"
    
    # 重命名为更友好的名称
    FINAL_IPA="${EXPORT_PATH}/${PROJECT_NAME}-$(date +%Y%m%d-%H%M%S).ipa"
    cp "$IPA_FILE" "$FINAL_IPA"
    echo "📋 最终文件: $FINAL_IPA"
    
    echo ""
    echo "🎉 构建完成!"
    echo ""
    echo "安装说明:"
    echo "1. 将IPA文件传输到你的iPhone"
    echo "2. 使用以下方法之一安装:"
    echo "   - AltStore (推荐): https://altstore.io/"
    echo "   - Sideloadly: https://sideloadly.io/"
    echo "   - 3uTools: https://www.3u.com/"
    echo "   - iTunes (如果有开发者证书)"
    echo ""
    echo "注意: 需要信任开发者证书才能运行应用"
    
else
    echo "❌ 未找到生成的IPA文件"
    exit 1
fi
