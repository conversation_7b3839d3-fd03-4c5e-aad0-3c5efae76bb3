name: Build iOS App

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Build iOS App
      run: |
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Release \
                   -destination 'generic/platform=iOS' \
                   -archivePath KeyPhone.xcarchive \
                   archive
    
    - name: Export IPA
      run: |
        # 创建导出配置文件
        cat > ExportOptions.plist << EOF
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>method</key>
            <string>development</string>
            <key>teamID</key>
            <string>YOUR_TEAM_ID</string>
            <key>compileBitcode</key>
            <false/>
            <key>uploadSymbols</key>
            <false/>
        </dict>
        </plist>
        EOF
        
        # 导出IPA
        xcodebuild -exportArchive \
                   -archivePath KeyPhone.xcarchive \
                   -exportPath ./build \
                   -exportOptionsPlist ExportOptions.plist
    
    - name: Upload IPA
      uses: actions/upload-artifact@v3
      with:
        name: KeyPhone-IPA
        path: ./build/*.ipa
