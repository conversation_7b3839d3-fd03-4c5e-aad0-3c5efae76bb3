name: Build iOS App

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4

    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Build iOS App
      run: |
        # 设置构建参数以避免签名问题
        xcodebuild -project KeyPhone.xcodeproj \
                   -scheme KeyPhone \
                   -configuration Release \
                   -destination 'generic/platform=iOS' \
                   -archivePath KeyPhone.xcarchive \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   CODE_SIGN_IDENTITY="" \
                   PROVISIONING_PROFILE="" \
                   archive
    
    - name: Export IPA
      run: |
        # 创建导出配置文件
        cat > ExportOptions.plist << EOF
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>method</key>
            <string>development</string>
            <key>compileBitcode</key>
            <false/>
            <key>uploadSymbols</key>
            <false/>
            <key>signingStyle</key>
            <string>manual</string>
            <key>stripSwiftSymbols</key>
            <true/>
        </dict>
        </plist>
        EOF

        # 创建构建目录
        mkdir -p ./build

        # 导出IPA
        xcodebuild -exportArchive \
                   -archivePath KeyPhone.xcarchive \
                   -exportPath ./build \
                   -exportOptionsPlist ExportOptions.plist \
                   -allowProvisioningUpdates
    
    - name: Upload IPA
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-IPA
        path: ./build/*.ipa
