import asyncio
import json
import logging
from bleak import BleakServer, BleakGATTCharacteristic, BleakGATTService
from bleak.backends.characteristic import BleakGATTCharacteristicProperties
import pyautogui
import keyboard
import sys
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 禁用pyautogui的安全特性
pyautogui.FAILSAFE = False

class KeyPhoneReceiver:
    def __init__(self):
        # 自定义服务和特征UUID（与iOS应用保持一致）
        self.SERVICE_UUID = "12345678-1234-1234-1234-123456789ABC"
        self.CHARACTERISTIC_UUID = "*************-4321-4321-CBA987654321"
        
        self.server = None
        self.characteristic = None
        
        # 特殊键映射
        self.special_key_map = {
            'enter': 'enter',
            'backspace': 'backspace',
            'delete': 'delete',
            'tab': 'tab',
            'escape': 'esc',
            'space': 'space',
            'leftArrow': 'left',
            'rightArrow': 'right',
            'upArrow': 'up',
            'downArrow': 'down',
            'home': 'home',
            'end': 'end',
            'pageUp': 'page up',
            'pageDown': 'page down',
            'f1': 'f1', 'f2': 'f2', 'f3': 'f3', 'f4': 'f4',
            'f5': 'f5', 'f6': 'f6', 'f7': 'f7', 'f8': 'f8',
            'f9': 'f9', 'f10': 'f10', 'f11': 'f11', 'f12': 'f12',
            'cmd': 'win',
            'ctrl': 'ctrl',
            'alt': 'alt',
            'shift': 'shift'
        }
    
    async def characteristic_write_callback(self, characteristic: BleakGATTCharacteristic, data: bytearray):
        """处理从iOS设备接收到的数据"""
        try:
            # 解析JSON数据
            json_data = data.decode('utf-8')
            command = json.loads(json_data)
            
            logger.info(f"Received command: {command}")
            
            # 根据命令类型处理
            if command['type'] == 'mouse':
                await self.handle_mouse_command(command)
            elif command['type'] == 'keyboard':
                await self.handle_keyboard_command(command)
            elif command['type'] == 'specialKey':
                await self.handle_special_key_command(command)
                
        except Exception as e:
            logger.error(f"Error processing command: {e}")
    
    async def handle_mouse_command(self, command):
        """处理鼠标命令"""
        try:
            delta_x = command.get('deltaX', 0)
            delta_y = command.get('deltaY', 0)
            left_click = command.get('leftClick', False)
            right_click = command.get('rightClick', False)
            scroll = command.get('scroll', 0)
            
            # 移动鼠标
            if delta_x != 0 or delta_y != 0:
                current_x, current_y = pyautogui.position()
                new_x = current_x + int(delta_x)
                new_y = current_y + int(delta_y)
                pyautogui.moveTo(new_x, new_y)
            
            # 处理点击
            if left_click:
                pyautogui.click(button='left')
            if right_click:
                pyautogui.click(button='right')
            
            # 处理滚轮
            if scroll != 0:
                pyautogui.scroll(int(scroll * 3))  # 放大滚轮效果
                
        except Exception as e:
            logger.error(f"Error handling mouse command: {e}")
    
    async def handle_keyboard_command(self, command):
        """处理键盘文本输入命令"""
        try:
            text = command.get('text', '')
            if text:
                pyautogui.write(text)
        except Exception as e:
            logger.error(f"Error handling keyboard command: {e}")
    
    async def handle_special_key_command(self, command):
        """处理特殊键命令"""
        try:
            key = command.get('key', '')
            if key in self.special_key_map:
                mapped_key = self.special_key_map[key]
                pyautogui.press(mapped_key)
        except Exception as e:
            logger.error(f"Error handling special key command: {e}")
    
    async def setup_server(self):
        """设置BLE服务器"""
        try:
            # 创建特征
            self.characteristic = BleakGATTCharacteristic(
                self.CHARACTERISTIC_UUID,
                BleakGATTCharacteristicProperties.write,
                None,
                [self.characteristic_write_callback]
            )
            
            # 创建服务
            service = BleakGATTService(
                self.SERVICE_UUID,
                [self.characteristic]
            )
            
            # 创建服务器
            self.server = BleakServer([service])
            
            logger.info("BLE server setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up server: {e}")
            raise
    
    async def start_server(self):
        """启动BLE服务器"""
        try:
            await self.setup_server()
            
            async with self.server as server:
                logger.info("KeyPhone PC Receiver started")
                logger.info("Waiting for iOS device connection...")
                
                # 保持服务器运行
                while True:
                    await asyncio.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Error running server: {e}")
    
    def run(self):
        """运行服务器"""
        try:
            asyncio.run(self.start_server())
        except Exception as e:
            logger.error(f"Failed to start server: {e}")

def main():
    print("KeyPhone PC Receiver")
    print("===================")
    print("This program will receive commands from your iPhone via Bluetooth")
    print("and simulate mouse and keyboard actions on your PC.")
    print()
    print("Make sure Bluetooth is enabled on your PC.")
    print("Press Ctrl+C to stop the server.")
    print()
    
    receiver = KeyPhoneReceiver()
    receiver.run()

if __name__ == "__main__":
    main()
