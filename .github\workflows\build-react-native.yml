name: Build React Native App

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-android:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
    
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
    
    - name: Install dependencies
      run: npm install
    
    - name: Generate Android project
      run: |
        npx react-native init KeyPhoneTemp --template react-native-template-typescript
        cp -r KeyPhoneTemp/android ./
        rm -rf KeyPhoneTemp
        
        # 复制我们的源代码
        mkdir -p android/app/src/main/assets
        
        # 更新Android配置
        cat > android/app/src/main/AndroidManifest.xml << 'EOF'
        <manifest xmlns:android="http://schemas.android.com/apk/res/android">
            <uses-permission android:name="android.permission.INTERNET" />
            <uses-permission android:name="android.permission.BLUETOOTH" />
            <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
            <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
            <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
            <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
            <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
            
            <application
              android:name=".MainApplication"
              android:label="@string/app_name"
              android:icon="@mipmap/ic_launcher"
              android:roundIcon="@mipmap/ic_launcher_round"
              android:allowBackup="false"
              android:theme="@style/AppTheme">
              <activity
                android:name=".MainActivity"
                android:label="@string/app_name"
                android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
                android:launchMode="singleTask"
                android:windowSoftInputMode="adjustResize"
                android:exported="true">
                <intent-filter>
                    <action android:name="android.intent.action.MAIN" />
                    <category android:name="android.intent.category.LAUNCHER" />
                </intent-filter>
              </activity>
            </application>
        </manifest>
        EOF
    
    - name: Build Android bundle
      run: |
        npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle
    
    - name: Build Android APK
      run: |
        cd android
        ./gradlew assembleRelease
    
    - name: Upload Android APK
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-Android-APK
        path: android/app/build/outputs/apk/release/app-release.apk
        retention-days: 30

  build-ios:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Install dependencies
      run: npm install
    
    - name: Generate iOS project
      run: |
        npx react-native init KeyPhoneTemp --template react-native-template-typescript
        cp -r KeyPhoneTemp/ios ./
        rm -rf KeyPhoneTemp
        
        # 更新iOS配置
        cat > ios/KeyPhone/Info.plist << 'EOF'
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>CFBundleDevelopmentRegion</key>
            <string>en</string>
            <key>CFBundleDisplayName</key>
            <string>KeyPhone</string>
            <key>CFBundleExecutable</key>
            <string>$(EXECUTABLE_NAME)</string>
            <key>CFBundleIdentifier</key>
            <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
            <key>CFBundleInfoDictionaryVersion</key>
            <string>6.0</string>
            <key>CFBundleName</key>
            <string>$(PRODUCT_NAME)</string>
            <key>CFBundlePackageType</key>
            <string>APPL</string>
            <key>CFBundleShortVersionString</key>
            <string>$(MARKETING_VERSION)</string>
            <key>CFBundleVersion</key>
            <string>$(CURRENT_PROJECT_VERSION)</string>
            <key>LSRequiresIPhoneOS</key>
            <true/>
            <key>NSBluetoothAlwaysUsageDescription</key>
            <string>This app needs Bluetooth access to connect to your PC for remote control functionality.</string>
            <key>NSBluetoothPeripheralUsageDescription</key>
            <string>This app needs Bluetooth access to connect to your PC for remote control functionality.</string>
            <key>NSLocationWhenInUseUsageDescription</key>
            <string>This app needs location access for Bluetooth functionality.</string>
            <key>UILaunchStoryboardName</key>
            <string>LaunchScreen</string>
            <key>UIRequiredDeviceCapabilities</key>
            <array>
                <string>armv7</string>
            </array>
            <key>UISupportedInterfaceOrientations</key>
            <array>
                <string>UIInterfaceOrientationPortrait</string>
                <string>UIInterfaceOrientationLandscapeLeft</string>
                <string>UIInterfaceOrientationLandscapeRight</string>
            </array>
            <key>UISupportedInterfaceOrientations~ipad</key>
            <array>
                <string>UIInterfaceOrientationPortrait</string>
                <string>UIInterfaceOrientationPortraitUpsideDown</string>
                <string>UIInterfaceOrientationLandscapeLeft</string>
                <string>UIInterfaceOrientationLandscapeRight</string>
            </array>
        </dict>
        </plist>
        EOF
    
    - name: Install iOS dependencies
      run: |
        cd ios
        pod install
    
    - name: Build iOS bundle
      run: |
        npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle
    
    - name: Build iOS app
      run: |
        cd ios
        xcodebuild -workspace KeyPhone.xcworkspace \
                   -scheme KeyPhone \
                   -configuration Release \
                   -destination 'generic/platform=iOS' \
                   -derivedDataPath ./DerivedData \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   CODE_SIGN_IDENTITY="" \
                   PROVISIONING_PROFILE="" \
                   build
    
    - name: Create IPA
      run: |
        cd ios
        APP_PATH=$(find ./DerivedData -name "KeyPhone.app" -type d | head -1)
        if [ -n "$APP_PATH" ]; then
          mkdir -p ./output/Payload
          cp -r "$APP_PATH" ./output/Payload/
          cd ./output
          zip -r KeyPhone-unsigned.ipa Payload/
          echo "✅ IPA created successfully"
        else
          echo "❌ No app found"
          exit 1
        fi
    
    - name: Upload iOS IPA
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-iOS-IPA
        path: ios/output/KeyPhone-unsigned.ipa
        retention-days: 30
    
    - name: Create installation guide
      run: |
        cat > INSTALL-GUIDE.md << 'EOF'
        # KeyPhone 安装指南
        
        ## Android 安装
        1. 下载 `KeyPhone-Android-APK` 文件
        2. 在Android设备上启用"未知来源"安装
        3. 安装APK文件
        
        ## iOS 安装
        1. 下载 `KeyPhone-iOS-IPA` 文件
        2. 使用以下工具之一安装：
           - AltStore: https://altstore.io/
           - Sideloadly: https://sideloadly.io/
           - 3uTools: https://www.3u.com/
        
        ## PC端设置
        1. 进入PC-Receiver目录
        2. 运行install.bat安装依赖
        3. 运行run.bat启动接收程序
        
        ## 使用说明
        1. 确保PC和手机蓝牙都已开启
        2. 在手机上打开KeyPhone应用
        3. 点击"连接PC"
        4. 连接成功后即可使用鼠标和键盘功能
        
        构建时间: $(date)
        EOF
    
    - name: Upload installation guide
      uses: actions/upload-artifact@v4
      with:
        name: Installation-Guide
        path: INSTALL-GUIDE.md
        retention-days: 30
