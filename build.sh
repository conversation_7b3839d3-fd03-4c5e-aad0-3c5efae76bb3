#!/bin/bash

# KeyPhone 项目构建脚本

echo "KeyPhone 项目构建脚本"
echo "===================="

# 检查Xcode是否安装
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: 未找到Xcode，请先安装Xcode"
    exit 1
fi

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python 3，请先安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ 环境检查通过"

# 构建iOS应用
echo ""
echo "构建iOS应用..."
echo "----------------"

# 清理之前的构建
xcodebuild clean -project KeyPhone.xcodeproj -scheme KeyPhone

# 构建项目
xcodebuild build -project KeyPhone.xcodeproj -scheme KeyPhone -destination 'platform=iOS Simulator,name=iPhone 14'

if [ $? -eq 0 ]; then
    echo "✅ iOS应用构建成功"
else
    echo "❌ iOS应用构建失败"
    exit 1
fi

# 安装PC端依赖
echo ""
echo "安装PC端依赖..."
echo "----------------"

cd PC-Receiver

# 创建虚拟环境（可选）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ PC端依赖安装成功"
else
    echo "❌ PC端依赖安装失败"
    exit 1
fi

# 运行测试
echo ""
echo "运行测试..."
echo "----------"

python test_receiver.py

cd ..

echo ""
echo "🎉 项目构建完成!"
echo ""
echo "下一步:"
echo "1. 使用Xcode将iOS应用安装到您的设备"
echo "2. 在PC上运行 PC-Receiver/run.bat 启动接收程序"
echo "3. 在iPhone上打开KeyPhone应用并连接"
