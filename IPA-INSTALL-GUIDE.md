# KeyPhone IPA 安装指南

由于你没有Mac，这里提供几种获取和安装IPA文件的方法。

## 方法1: 使用GitHub Actions自动构建 ⭐ 推荐

### 步骤：
1. **上传代码到GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/你的用户名/keyphone.git
   git push -u origin main
   ```

2. **触发自动构建**
   - GitHub会自动运行构建流程
   - 在Actions页面查看构建进度
   - 构建完成后下载IPA文件

3. **下载IPA**
   - 进入GitHub仓库的Actions页面
   - 点击最新的构建任务
   - 下载"KeyPhone-IPA"文件

## 方法2: 使用在线构建服务

### Codemagic (推荐)
1. 访问 https://codemagic.io/
2. 使用GitHub账户登录
3. 连接你的KeyPhone仓库
4. 配置iOS构建：
   ```yaml
   workflows:
     ios-workflow:
       name: iOS Workflow
       max_build_duration: 120
       environment:
         ios_signing:
           distribution_type: development
         vars:
           XCODE_WORKSPACE: "KeyPhone.xcodeproj"
           XCODE_SCHEME: "KeyPhone"
       scripts:
         - name: Build iOS
           script: |
             xcodebuild build -project KeyPhone.xcodeproj -scheme KeyPhone
       artifacts:
         - build/ios/ipa/*.ipa
   ```

### AppCenter (Microsoft)
1. 访问 https://appcenter.ms/
2. 创建新的iOS应用
3. 连接GitHub仓库
4. 配置构建设置
5. 启动构建并下载IPA

## 方法3: 租用云端Mac

### MacinCloud
- **网址**: https://www.macincloud.com/
- **价格**: $1-3/小时
- **步骤**:
  1. 注册账户并选择套餐
  2. 通过远程桌面连接
  3. 上传项目文件
  4. 使用Xcode编译
  5. 运行 `./build-ipa.sh` 脚本

### AWS EC2 Mac实例
- 如果你有AWS账户，可以启动Mac实例
- 按小时计费，适合一次性使用

## 方法4: 请朋友帮忙编译

如果你有朋友有Mac，可以：
1. 将项目文件发送给他们
2. 让他们运行 `./build-ipa.sh` 脚本
3. 获取生成的IPA文件

## IPA安装方法

### 1. AltStore (推荐) ⭐
**优点**: 免费，无需越狱，支持自动刷新
**步骤**:
1. 下载AltStore: https://altstore.io/
2. 在电脑上安装AltServer
3. 在iPhone上安装AltStore应用
4. 使用AltStore安装KeyPhone.ipa

### 2. Sideloadly
**优点**: 简单易用，支持Windows和Mac
**步骤**:
1. 下载Sideloadly: https://sideloadly.io/
2. 连接iPhone到电脑
3. 拖拽IPA文件到Sideloadly
4. 输入Apple ID并安装

### 3. 3uTools
**优点**: 功能丰富的iOS管理工具
**步骤**:
1. 下载3uTools: https://www.3u.com/
2. 连接iPhone
3. 使用"安装本地应用"功能
4. 选择IPA文件安装

### 4. Xcode (如果有Mac)
```bash
# 使用Xcode命令行工具安装
xcrun devicectl device install app --device [设备ID] KeyPhone.ipa
```

## 信任开发者证书

安装后需要信任证书：
1. 打开iPhone设置
2. 进入"通用" > "VPN与设备管理"
3. 找到开发者证书
4. 点击"信任"

## 注意事项

### 证书有效期
- 免费Apple ID: 7天后需要重新安装
- 付费开发者账户: 1年有效期

### 设备限制
- 免费账户最多3台设备
- 付费账户最多100台设备

### 应用限制
- 免费账户同时最多3个应用
- 某些功能可能受限

## 故障排除

### 安装失败
1. 检查设备存储空间
2. 确保iOS版本兼容
3. 重启设备后重试

### 应用闪退
1. 检查证书是否过期
2. 重新信任开发者证书
3. 重新安装应用

### 无法连接蓝牙
1. 确保蓝牙权限已授予
2. 检查PC端程序是否运行
3. 重启蓝牙服务

## 推荐流程

对于没有Mac的用户，推荐以下流程：

1. **使用GitHub Actions** (免费)
   - 上传代码到GitHub
   - 等待自动构建完成
   - 下载IPA文件

2. **使用AltStore安装** (免费)
   - 下载并设置AltStore
   - 安装KeyPhone应用
   - 每7天刷新一次证书

这样你就可以在没有Mac的情况下使用KeyPhone应用了！
