name: Build Unsigned IPA

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-unsigned:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Show Xcode version
      run: xcodebuild -version
    
    - name: Clean build directory
      run: |
        rm -rf build
        mkdir -p build
    
    - name: List available schemes
      run: |
        xcodebuild -project KeyPhone.xcodeproj -list

    - name: Build unsigned IPA
      run: |
        # 构建项目为app文件
        xcodebuild -project KeyPhone.xcodeproj \
                   -target KeyPhone \
                   -configuration Release \
                   -destination 'generic/platform=iOS' \
                   -derivedDataPath ./DerivedData \
                   CODE_SIGNING_REQUIRED=NO \
                   CODE_SIGNING_ALLOWED=NO \
                   CODE_SIGN_IDENTITY="" \
                   PROVISIONING_PROFILE="" \
                   build
        
        # 查找生成的app文件
        APP_PATH=$(find ./DerivedData -name "*.app" | head -1)
        echo "Found app at: $APP_PATH"
        
        if [ -z "$APP_PATH" ]; then
          echo "Error: No .app file found"
          exit 1
        fi
        
        # 创建Payload目录结构
        mkdir -p ./build/Payload
        cp -r "$APP_PATH" ./build/Payload/
        
        # 创建IPA文件
        cd ./build
        zip -r KeyPhone-unsigned.ipa Payload/
        
        # 验证IPA文件
        if [ -f "KeyPhone-unsigned.ipa" ]; then
          echo "✅ IPA created successfully"
          ls -la KeyPhone-unsigned.ipa
        else
          echo "❌ Failed to create IPA"
          exit 1
        fi
    
    - name: Upload unsigned IPA
      uses: actions/upload-artifact@v4
      with:
        name: KeyPhone-Unsigned-IPA
        path: ./build/KeyPhone-unsigned.ipa
        retention-days: 30
    
    - name: Create release info
      run: |
        echo "# KeyPhone Unsigned IPA" > ./build/README.md
        echo "" >> ./build/README.md
        echo "这是一个未签名的IPA文件，需要使用以下工具之一进行安装：" >> ./build/README.md
        echo "" >> ./build/README.md
        echo "## 安装方法：" >> ./build/README.md
        echo "1. **AltStore** (推荐): https://altstore.io/" >> ./build/README.md
        echo "2. **Sideloadly**: https://sideloadly.io/" >> ./build/README.md
        echo "3. **3uTools**: https://www.3u.com/" >> ./build/README.md
        echo "" >> ./build/README.md
        echo "## 注意事项：" >> ./build/README.md
        echo "- 需要Apple ID进行签名" >> ./build/README.md
        echo "- 免费账户7天后需要重新安装" >> ./build/README.md
        echo "- 安装后需要在设置中信任开发者证书" >> ./build/README.md
        echo "" >> ./build/README.md
        echo "构建时间: $(date)" >> ./build/README.md
        echo "构建版本: ${{ github.sha }}" >> ./build/README.md
    
    - name: Upload installation guide
      uses: actions/upload-artifact@v4
      with:
        name: Installation-Guide
        path: ./build/README.md
        retention-days: 30
