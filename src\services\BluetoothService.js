import BleManager from 'react-native-ble-manager';
import {PermissionsAndroid, Platform, Alert} from 'react-native';

class BluetoothService {
  constructor() {
    this.isConnected = false;
    this.connectedDevice = null;
    this.serviceUUID = '12345678-1234-1234-1234-123456789ABC';
    this.characteristicUUID = '*************-4321-4321-CBA987654321';
    this.listeners = [];
  }

  async initialize() {
    try {
      await BleManager.start({showAlert: false});
      console.log('BLE Manager initialized');

      if (Platform.OS === 'android') {
        await this.requestPermissions();
      }

      // 添加事件监听器
      BleManager.addListener('BleManagerDidUpdateState', this.handleStateChange);
      BleManager.addListener('BleManagerDiscoverPeripheral', this.handleDiscoverPeripheral);
      BleManager.addListener('BleManagerConnectPeripheral', this.handleConnectPeripheral);
      BleManager.addListener('BleManagerDisconnectPeripheral', this.handleDisconnectPeripheral);

      return true;
    } catch (error) {
      console.error('Failed to initialize BLE:', error);
      return false;
    }
  }

  async requestPermissions() {
    if (Platform.OS === 'android') {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADVERTISE,
      ];

      const granted = await PermissionsAndroid.requestMultiple(permissions);
      
      const allGranted = Object.values(granted).every(
        permission => permission === PermissionsAndroid.RESULTS.GRANTED
      );

      if (!allGranted) {
        Alert.alert('权限错误', '需要蓝牙和位置权限才能正常工作');
        return false;
      }
    }
    return true;
  }

  handleStateChange = (args) => {
    console.log('BLE state changed:', args.state);
    this.notifyListeners('stateChange', args.state);
  };

  handleDiscoverPeripheral = (peripheral) => {
    console.log('Discovered peripheral:', peripheral.name);
    if (peripheral.name && peripheral.name.includes('KeyPhone')) {
      this.connectToDevice(peripheral.id);
    }
  };

  handleConnectPeripheral = (args) => {
    console.log('Connected to peripheral:', args.peripheral);
    this.isConnected = true;
    this.connectedDevice = args.peripheral;
    this.notifyListeners('connected', args.peripheral);
  };

  handleDisconnectPeripheral = (args) => {
    console.log('Disconnected from peripheral:', args.peripheral);
    this.isConnected = false;
    this.connectedDevice = null;
    this.notifyListeners('disconnected', args.peripheral);
  };

  async startScan() {
    try {
      await BleManager.scan([this.serviceUUID], 10, true);
      console.log('Started scanning for devices...');
      return true;
    } catch (error) {
      console.error('Failed to start scan:', error);
      return false;
    }
  }

  async connectToDevice(deviceId) {
    try {
      await BleManager.connect(deviceId);
      console.log('Connected to device:', deviceId);
      
      // 发现服务和特征
      await BleManager.retrieveServices(deviceId);
      
      return true;
    } catch (error) {
      console.error('Failed to connect to device:', error);
      return false;
    }
  }

  async disconnect() {
    if (this.connectedDevice) {
      try {
        await BleManager.disconnect(this.connectedDevice);
        return true;
      } catch (error) {
        console.error('Failed to disconnect:', error);
        return false;
      }
    }
    return false;
  }

  async sendMouseData(deltaX, deltaY, leftClick = false, rightClick = false, scroll = 0) {
    if (!this.isConnected || !this.connectedDevice) {
      console.warn('Not connected to any device');
      return false;
    }

    const command = {
      type: 'mouse',
      deltaX,
      deltaY,
      leftClick,
      rightClick,
      scroll,
    };

    return this.sendCommand(command);
  }

  async sendKeyboardData(text) {
    if (!this.isConnected || !this.connectedDevice) {
      console.warn('Not connected to any device');
      return false;
    }

    const command = {
      type: 'keyboard',
      text,
    };

    return this.sendCommand(command);
  }

  async sendSpecialKey(key) {
    if (!this.isConnected || !this.connectedDevice) {
      console.warn('Not connected to any device');
      return false;
    }

    const command = {
      type: 'specialKey',
      key,
    };

    return this.sendCommand(command);
  }

  async sendCommand(command) {
    try {
      const data = JSON.stringify(command);
      const bytes = Array.from(Buffer.from(data, 'utf8'));
      
      await BleManager.write(
        this.connectedDevice,
        this.serviceUUID,
        this.characteristicUUID,
        bytes
      );
      
      return true;
    } catch (error) {
      console.error('Failed to send command:', error);
      return false;
    }
  }

  addListener(callback) {
    this.listeners.push(callback);
  }

  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('Error in listener:', error);
      }
    });
  }

  cleanup() {
    BleManager.removeAllListeners('BleManagerDidUpdateState');
    BleManager.removeAllListeners('BleManagerDiscoverPeripheral');
    BleManager.removeAllListeners('BleManagerConnectPeripheral');
    BleManager.removeAllListeners('BleManagerDisconnectPeripheral');
  }
}

export default new BluetoothService();
