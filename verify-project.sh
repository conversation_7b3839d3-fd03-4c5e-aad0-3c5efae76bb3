#!/bin/bash

echo "KeyPhone 项目验证脚本"
echo "===================="

# 检查必要文件
echo "检查项目文件..."

required_files=(
    "KeyPhone.xcodeproj/project.pbxproj"
    "KeyPhone.xcodeproj/xcshareddata/xcschemes/KeyPhone.xcscheme"
    "KeyPhone/Info.plist"
    "KeyPhone/AppDelegate.swift"
    "KeyPhone/SceneDelegate.swift"
    "KeyPhone/MainViewController.swift"
    "KeyPhone/BluetoothManager.swift"
    "KeyPhone/MouseViewController.swift"
    "KeyPhone/KeyboardViewController.swift"
    "KeyPhone/Assets.xcassets/Contents.json"
    "KeyPhone/Base.lproj/LaunchScreen.storyboard"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo ""
    echo "✅ 所有必要文件都存在"
else
    echo ""
    echo "❌ 缺失 ${#missing_files[@]} 个文件"
    exit 1
fi

# 检查项目结构
echo ""
echo "检查项目结构..."
if command -v xcodebuild &> /dev/null; then
    echo "✅ 找到 xcodebuild"
    
    echo ""
    echo "项目信息:"
    xcodebuild -project KeyPhone.xcodeproj -list
    
    echo ""
    echo "尝试验证构建..."
    xcodebuild -project KeyPhone.xcodeproj \
               -scheme KeyPhone \
               -configuration Debug \
               -destination 'platform=iOS Simulator,name=iPhone 14' \
               -derivedDataPath ./DerivedData \
               CODE_SIGNING_REQUIRED=NO \
               CODE_SIGNING_ALLOWED=NO \
               CODE_SIGN_IDENTITY="" \
               PROVISIONING_PROFILE="" \
               -dry-run \
               build
    
    if [ $? -eq 0 ]; then
        echo "✅ 项目验证成功"
    else
        echo "❌ 项目验证失败"
        exit 1
    fi
else
    echo "⚠️  未找到 xcodebuild，跳过构建验证"
fi

echo ""
echo "🎉 项目验证完成!"
