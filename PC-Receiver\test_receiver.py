#!/usr/bin/env python3
"""
KeyPhone PC Receiver 测试脚本
用于测试蓝牙连接和命令处理功能
"""

import asyncio
import json
import logging
import time
from main import KeyPhoneReceiver

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TestKeyPhoneReceiver(KeyPhoneReceiver):
    """测试版本的KeyPhone接收器"""
    
    def __init__(self):
        super().__init__()
        self.test_commands = [
            # 鼠标移动测试
            {
                "type": "mouse",
                "deltaX": 10,
                "deltaY": 0,
                "leftClick": False,
                "rightClick": False,
                "scroll": 0
            },
            # 鼠标点击测试
            {
                "type": "mouse",
                "deltaX": 0,
                "deltaY": 0,
                "leftClick": True,
                "rightClick": False,
                "scroll": 0
            },
            # 键盘输入测试
            {
                "type": "keyboard",
                "text": "Hello from KeyPhone!"
            },
            # 特殊键测试
            {
                "type": "specialKey",
                "key": "enter"
            },
            # 滚轮测试
            {
                "type": "mouse",
                "deltaX": 0,
                "deltaY": 0,
                "leftClick": False,
                "rightClick": False,
                "scroll": 3
            }
        ]
    
    async def run_tests(self):
        """运行测试命令"""
        print("开始测试KeyPhone命令处理...")
        print("=" * 50)
        
        for i, command in enumerate(self.test_commands, 1):
            print(f"\n测试 {i}: {command['type']} 命令")
            print(f"命令内容: {json.dumps(command, indent=2)}")
            
            try:
                # 模拟接收到的数据
                data = json.dumps(command).encode('utf-8')
                await self.characteristic_write_callback(None, bytearray(data))
                print("✅ 命令执行成功")
                
                # 等待一段时间观察效果
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"❌ 命令执行失败: {e}")
        
        print("\n" + "=" * 50)
        print("测试完成!")
    
    def run_test_mode(self):
        """运行测试模式"""
        try:
            asyncio.run(self.run_tests())
        except Exception as e:
            logger.error(f"测试失败: {e}")

def test_command_parsing():
    """测试命令解析功能"""
    print("测试命令解析功能...")
    
    test_cases = [
        '{"type": "mouse", "deltaX": 10, "deltaY": 5, "leftClick": true, "rightClick": false, "scroll": 0}',
        '{"type": "keyboard", "text": "测试文本"}',
        '{"type": "specialKey", "key": "enter"}',
        '{"invalid": "json"}',  # 无效JSON测试
        'invalid json string'   # 无效格式测试
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case}")
        try:
            command = json.loads(test_case)
            print(f"✅ 解析成功: {command}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_special_key_mapping():
    """测试特殊键映射"""
    print("\n测试特殊键映射...")
    
    receiver = KeyPhoneReceiver()
    test_keys = ['enter', 'backspace', 'tab', 'escape', 'f1', 'ctrl', 'invalid_key']
    
    for key in test_keys:
        if key in receiver.special_key_map:
            mapped = receiver.special_key_map[key]
            print(f"✅ {key} -> {mapped}")
        else:
            print(f"❌ {key} -> 未找到映射")

def performance_test():
    """性能测试"""
    print("\n性能测试...")
    
    # 测试JSON编码/解码性能
    test_command = {
        "type": "mouse",
        "deltaX": 10.5,
        "deltaY": -5.2,
        "leftClick": False,
        "rightClick": False,
        "scroll": 0
    }
    
    start_time = time.time()
    iterations = 1000
    
    for _ in range(iterations):
        json_str = json.dumps(test_command)
        parsed = json.loads(json_str)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"JSON编码/解码性能测试:")
    print(f"  迭代次数: {iterations}")
    print(f"  总耗时: {duration:.4f}秒")
    print(f"  平均耗时: {duration/iterations*1000:.4f}毫秒/次")

def main():
    """主测试函数"""
    print("KeyPhone PC Receiver 测试程序")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 命令处理测试（需要鼠标键盘权限）")
        print("2. 命令解析测试")
        print("3. 特殊键映射测试")
        print("4. 性能测试")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            print("\n⚠️  警告: 此测试将控制您的鼠标和键盘")
            print("请确保您已保存所有重要工作")
            confirm = input("继续测试? (y/N): ").strip().lower()
            if confirm == 'y':
                receiver = TestKeyPhoneReceiver()
                receiver.run_test_mode()
            else:
                print("测试已取消")
        
        elif choice == '2':
            test_command_parsing()
        
        elif choice == '3':
            test_special_key_mapping()
        
        elif choice == '4':
            performance_test()
        
        elif choice == '5':
            print("退出测试程序")
            break
        
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
