import UIKit

class MainViewController: UIViewController {
    
    private let bluetoothManager = BluetoothManager.shared
    private var connectionStatusLabel: UILabel!
    private var connectButton: UIButton!
    private var mouseButton: UIButton!
    private var keyboardButton: UIButton!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBluetoothManager()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "KeyPhone"
        
        // Connection status label
        connectionStatusLabel = UILabel()
        connectionStatusLabel.text = "未连接"
        connectionStatusLabel.textAlignment = .center
        connectionStatusLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        connectionStatusLabel.textColor = .systemRed
        connectionStatusLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(connectionStatusLabel)
        
        // Connect button
        connectButton = UIButton(type: .system)
        connectButton.setTitle("连接PC", for: .normal)
        connectButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        connectButton.backgroundColor = .systemBlue
        connectButton.setTitleColor(.white, for: .normal)
        connectButton.layer.cornerRadius = 12
        connectButton.translatesAutoresizingMaskIntoConstraints = false
        connectButton.addTarget(self, action: #selector(connectButtonTapped), for: .touchUpInside)
        view.addSubview(connectButton)
        
        // Mouse control button
        mouseButton = UIButton(type: .system)
        mouseButton.setTitle("鼠标控制", for: .normal)
        mouseButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        mouseButton.backgroundColor = .systemGreen
        mouseButton.setTitleColor(.white, for: .normal)
        mouseButton.layer.cornerRadius = 12
        mouseButton.translatesAutoresizingMaskIntoConstraints = false
        mouseButton.addTarget(self, action: #selector(mouseButtonTapped), for: .touchUpInside)
        mouseButton.isEnabled = false
        mouseButton.alpha = 0.5
        view.addSubview(mouseButton)
        
        // Keyboard control button
        keyboardButton = UIButton(type: .system)
        keyboardButton.setTitle("键盘控制", for: .normal)
        keyboardButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        keyboardButton.backgroundColor = .systemOrange
        keyboardButton.setTitleColor(.white, for: .normal)
        keyboardButton.layer.cornerRadius = 12
        keyboardButton.translatesAutoresizingMaskIntoConstraints = false
        keyboardButton.addTarget(self, action: #selector(keyboardButtonTapped), for: .touchUpInside)
        keyboardButton.isEnabled = false
        keyboardButton.alpha = 0.5
        view.addSubview(keyboardButton)
        
        // Setup constraints
        NSLayoutConstraint.activate([
            connectionStatusLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            connectionStatusLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 50),
            
            connectButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            connectButton.topAnchor.constraint(equalTo: connectionStatusLabel.bottomAnchor, constant: 30),
            connectButton.widthAnchor.constraint(equalToConstant: 200),
            connectButton.heightAnchor.constraint(equalToConstant: 50),
            
            mouseButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            mouseButton.topAnchor.constraint(equalTo: connectButton.bottomAnchor, constant: 40),
            mouseButton.widthAnchor.constraint(equalToConstant: 200),
            mouseButton.heightAnchor.constraint(equalToConstant: 50),
            
            keyboardButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            keyboardButton.topAnchor.constraint(equalTo: mouseButton.bottomAnchor, constant: 20),
            keyboardButton.widthAnchor.constraint(equalToConstant: 200),
            keyboardButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    private func setupBluetoothManager() {
        bluetoothManager.delegate = self
    }
    
    @objc private func connectButtonTapped() {
        if bluetoothManager.isConnected {
            bluetoothManager.disconnect()
        } else {
            bluetoothManager.startScanning()
        }
    }
    
    @objc private func mouseButtonTapped() {
        let mouseVC = MouseViewController()
        navigationController?.pushViewController(mouseVC, animated: true)
    }
    
    @objc private func keyboardButtonTapped() {
        let keyboardVC = KeyboardViewController()
        navigationController?.pushViewController(keyboardVC, animated: true)
    }
    
    private func updateConnectionStatus(_ isConnected: Bool) {
        DispatchQueue.main.async {
            if isConnected {
                self.connectionStatusLabel.text = "已连接"
                self.connectionStatusLabel.textColor = .systemGreen
                self.connectButton.setTitle("断开连接", for: .normal)
                self.connectButton.backgroundColor = .systemRed
                self.mouseButton.isEnabled = true
                self.mouseButton.alpha = 1.0
                self.keyboardButton.isEnabled = true
                self.keyboardButton.alpha = 1.0
            } else {
                self.connectionStatusLabel.text = "未连接"
                self.connectionStatusLabel.textColor = .systemRed
                self.connectButton.setTitle("连接PC", for: .normal)
                self.connectButton.backgroundColor = .systemBlue
                self.mouseButton.isEnabled = false
                self.mouseButton.alpha = 0.5
                self.keyboardButton.isEnabled = false
                self.keyboardButton.alpha = 0.5
            }
        }
    }
}

// MARK: - BluetoothManagerDelegate
extension MainViewController: BluetoothManagerDelegate {
    func bluetoothManagerDidConnect() {
        updateConnectionStatus(true)
    }
    
    func bluetoothManagerDidDisconnect() {
        updateConnectionStatus(false)
    }
    
    func bluetoothManagerDidFailToConnect(error: Error) {
        DispatchQueue.main.async {
            let alert = UIAlertController(title: "连接失败", message: error.localizedDescription, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            self.present(alert, animated: true)
        }
    }
}
