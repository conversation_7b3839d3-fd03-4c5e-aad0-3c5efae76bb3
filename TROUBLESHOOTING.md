# KeyPhone 故障排除指南

## GitHub Actions 构建问题

### 问题1: actions/upload-artifact@v3 已弃用
**错误信息**: `This request has been automatically failed because it uses a deprecated version of actions/upload-artifact: v3`

**解决方案**: ✅ 已修复
- 更新到 `actions/upload-artifact@v4`
- 更新到 `actions/checkout@v4`

### 问题2: 代码签名失败
**错误信息**: `Code signing is required for product type 'Application'`

**解决方案**: 
```yaml
# 在xcodebuild命令中添加以下参数
CODE_SIGNING_REQUIRED=NO \
CODE_SIGNING_ALLOWED=NO \
CODE_SIGN_IDENTITY="" \
PROVISIONING_PROFILE=""
```

### 问题3: 找不到scheme
**错误信息**: `The project does not contain a scheme named 'KeyPhone'`

**解决方案**: 
1. 检查项目文件是否正确上传
2. 确认scheme名称正确
3. 可能需要在Xcode中共享scheme

## IPA 安装问题

### 问题1: "无法安装应用"
**可能原因**:
- 设备存储空间不足
- iOS版本不兼容
- IPA文件损坏

**解决方案**:
1. 清理设备存储空间
2. 检查iOS版本要求（需要iOS 13.0+）
3. 重新下载IPA文件

### 问题2: "未受信任的开发者"
**错误信息**: 应用无法打开，显示未受信任的开发者

**解决方案**:
1. 打开设置 → 通用 → VPN与设备管理
2. 找到对应的开发者证书
3. 点击"信任"按钮

### 问题3: AltStore安装失败
**可能原因**:
- AltServer未运行
- 设备未连接到同一WiFi
- Apple ID问题

**解决方案**:
1. 确保电脑上的AltServer正在运行
2. 确保iPhone和电脑在同一WiFi网络
3. 重新登录Apple ID
4. 检查是否超过3个应用限制

## 蓝牙连接问题

### 问题1: 无法发现PC
**可能原因**:
- PC蓝牙未开启
- PC端程序未运行
- 蓝牙权限未授予

**解决方案**:
1. 确保PC蓝牙已开启
2. 运行PC端程序 `python main.py`
3. 在iPhone设置中授予蓝牙权限

### 问题2: 连接后立即断开
**可能原因**:
- 距离太远
- 蓝牙干扰
- 程序异常

**解决方案**:
1. 缩短设备距离（建议5米内）
2. 关闭其他蓝牙设备
3. 重启两端程序

### 问题3: 鼠标/键盘无响应
**可能原因**:
- PC端权限不足
- 防火墙阻止
- 程序异常

**解决方案**:
1. 以管理员身份运行PC端程序
2. 关闭防火墙或添加例外
3. 检查PC端程序日志

## PC端程序问题

### 问题1: Python依赖安装失败
**错误信息**: `pip install failed`

**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者逐个安装
pip install bleak==0.21.1
pip install pyautogui==0.9.54
pip install keyboard==0.13.5
```

### 问题2: 蓝牙模块初始化失败
**错误信息**: `Bluetooth adapter not found`

**解决方案**:
1. 确保PC有蓝牙适配器
2. 更新蓝牙驱动程序
3. 重启蓝牙服务

### 问题3: pyautogui权限问题
**错误信息**: `Permission denied` 或鼠标键盘无法控制

**解决方案**:
1. Windows: 以管理员身份运行
2. 关闭UAC或添加程序到白名单
3. 检查安全软件是否阻止

## iOS应用问题

### 问题1: 应用闪退
**可能原因**:
- 证书过期
- 内存不足
- 系统不兼容

**解决方案**:
1. 重新安装应用
2. 重启设备
3. 检查iOS版本兼容性

### 问题2: 蓝牙权限被拒绝
**错误信息**: 无法访问蓝牙

**解决方案**:
1. 设置 → 隐私与安全性 → 蓝牙
2. 找到KeyPhone应用
3. 开启蓝牙权限

### 问题3: 界面显示异常
**可能原因**:
- 屏幕尺寸适配问题
- 系统主题影响

**解决方案**:
1. 重启应用
2. 切换系统主题（深色/浅色）
3. 重新安装应用

## 性能优化

### 降低延迟
1. 减少设备距离
2. 关闭其他蓝牙设备
3. 调整鼠标灵敏度

### 提高稳定性
1. 保持设备电量充足
2. 关闭不必要的后台应用
3. 定期重启蓝牙连接

## 常见错误代码

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| -1001 | 网络超时 | 检查网络连接 |
| -1004 | 无法连接服务器 | 检查PC端程序 |
| -1009 | 网络连接丢失 | 重新连接WiFi |

## 获取帮助

如果以上方法都无法解决问题：

1. **查看日志**:
   - iOS: 连接Xcode查看控制台日志
   - PC: 查看命令行输出

2. **提交Issue**:
   - 访问GitHub仓库
   - 提供详细的错误信息和步骤

3. **联系支持**:
   - 发送邮件描述问题
   - 附上相关日志文件

## 调试模式

### 启用详细日志
PC端程序添加调试参数：
```python
logging.basicConfig(level=logging.DEBUG)
```

### iOS调试
使用Xcode连接设备查看实时日志：
1. 连接iPhone到Mac
2. 打开Xcode → Window → Devices and Simulators
3. 选择设备查看日志
