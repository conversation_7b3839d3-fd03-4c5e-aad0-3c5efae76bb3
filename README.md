# KeyPhone - 蓝牙鼠标键盘控制器

KeyPhone 是一个iOS应用，可以通过蓝牙连接将你的iPhone变成PC的无线鼠标和键盘。

## 功能特性

- 🖱️ **鼠标控制**: 触摸板手势控制鼠标移动、点击和滚轮
- ⌨️ **键盘控制**: 文本输入和特殊按键支持
- 🔗 **蓝牙连接**: 使用低功耗蓝牙(BLE)进行稳定连接
- 🎯 **高精度**: 可调节的鼠标灵敏度
- 📱 **直观界面**: 简洁易用的用户界面

## 系统要求

### iOS端
- iOS 13.0 或更高版本
- 支持蓝牙4.0的设备
- Xcode 12.0 或更高版本（用于编译）

### PC端
- Windows 10 或更高版本
- 支持蓝牙4.0的PC
- Python 3.8 或更高版本

## 安装说明

### 1. iOS应用安装

1. 使用Xcode打开 `KeyPhone.xcodeproj`
2. 连接你的iPhone到Mac
3. 选择你的设备作为目标
4. 点击运行按钮编译并安装应用

### 2. PC端程序安装

1. 确保PC已安装Python 3.8或更高版本
2. 进入 `PC-Receiver` 目录
3. 双击运行 `install.bat` 安装依赖包
4. 或者手动运行: `pip install -r requirements.txt`

## 使用方法

### 1. 启动PC端接收程序

1. 确保PC的蓝牙已开启
2. 进入 `PC-Receiver` 目录
3. 双击运行 `run.bat`
4. 或者手动运行: `python main.py`
5. 程序将等待iPhone连接

### 2. 使用iPhone应用

1. 在iPhone上打开KeyPhone应用
2. 确保iPhone的蓝牙已开启
3. 点击"连接PC"按钮
4. 等待连接成功（状态显示为"已连接"）
5. 现在可以使用鼠标和键盘功能了

### 3. 鼠标控制

- **移动鼠标**: 在触摸板区域滑动手指
- **左键点击**: 点击触摸板区域或按"左键"按钮
- **右键点击**: 按"右键"按钮
- **滚轮**: 在滚轮区域上下滑动
- **调节灵敏度**: 点击右上角"设置"按钮

### 4. 键盘控制

- **文本输入**: 在文本框中输入文字，按"发送"或回车键发送
- **特殊按键**: 使用界面上的按钮（Tab、Enter、Backspace等）
- **方向键**: 使用屏幕上的方向键按钮
- **功能键**: F1-F12功能键
- **修饰键**: Ctrl、Alt、Cmd、Shift

## 技术架构

### iOS端技术栈
- **Swift**: 主要编程语言
- **Core Bluetooth**: 蓝牙通信框架
- **UIKit**: 用户界面框架

### PC端技术栈
- **Python**: 主要编程语言
- **Bleak**: 蓝牙低功耗库
- **PyAutoGUI**: 鼠标键盘模拟库
- **Keyboard**: 键盘事件处理库

### 通信协议
使用JSON格式通过蓝牙传输命令：

```json
// 鼠标命令
{
  "type": "mouse",
  "deltaX": 10.5,
  "deltaY": -5.2,
  "leftClick": false,
  "rightClick": false,
  "scroll": 0
}

// 键盘文本命令
{
  "type": "keyboard",
  "text": "Hello World"
}

// 特殊键命令
{
  "type": "specialKey",
  "key": "enter"
}
```

## 故障排除

### 连接问题
1. 确保两个设备的蓝牙都已开启
2. 确保设备距离在10米以内
3. 重启蓝牙或重新启动应用
4. 检查PC端程序是否正常运行

### 性能问题
1. 调整鼠标灵敏度设置
2. 确保设备电量充足
3. 减少蓝牙干扰源

### 权限问题
1. 确保iOS应用有蓝牙权限
2. 确保PC端程序有管理员权限（如需要）

## 开发说明

### 自定义UUID
如果需要修改蓝牙服务UUID，请同时修改以下文件：
- iOS: `BluetoothManager.swift` 中的 `serviceUUID` 和 `characteristicUUID`
- PC: `main.py` 中的 `SERVICE_UUID` 和 `CHARACTERISTIC_UUID`

### 添加新功能
1. 在iOS端的 `BluetoothManager.swift` 中添加新的命令类型
2. 在PC端的 `main.py` 中添加对应的处理逻辑
3. 更新UI界面以支持新功能

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系我们。
