import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import MainScreen from './src/screens/MainScreen';
import MouseScreen from './src/screens/MouseScreen';
import KeyboardScreen from './src/screens/KeyboardScreen';

const Stack = createStackNavigator();

const App = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Main"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#007AFF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}>
        <Stack.Screen
          name="Main"
          component={MainScreen}
          options={{title: 'KeyPhone'}}
        />
        <Stack.Screen
          name="Mouse"
          component={MouseScreen}
          options={{title: '鼠标控制'}}
        />
        <Stack.Screen
          name="Keyboard"
          component={KeyboardScreen}
          options={{title: '键盘控制'}}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default App;
